% 信息年龄(AoI)对比实验主程序
% 对比分层RL、标准DQN、演员-评论家算法在三种场景中的AoI性能
% 生成AoI-能耗Pareto前沿图和三种场景下的实验结果

function aoi_comparison_experiment()
    close all;
    clear;
    clc;
    
    fprintf('=== 信息年龄(AoI)对比实验 ===\n');
    fprintf('对比算法: 分层RL、标准DQN、演员-评论家\n');
    fprintf('测试场景: 静态监测、动态转换、周期性运动\n\n');
    
    % 设置随机种子确保可重现性
    rng(42);
    
    % 定义实验参数
    algorithms = {'hierarchical_rl', 'dqn', 'actor_critic'};
    algorithm_names = {'分层RL', '标准DQN', '演员-评论家'};
    scenarios = {'static', 'dynamic', 'periodic'};
    scenario_names = {'静态监测', '动态转换', '周期性运动'};
    
    num_runs = 10; % 每个配置运行10次
    
    % 存储结果
    aoi_results = zeros(length(algorithms), length(scenarios), num_runs);
    energy_results = zeros(length(algorithms), length(scenarios), num_runs);
    
    % 运行实验
    fprintf('开始运行AoI对比实验...\n');
    for s = 1:length(scenarios)
        fprintf('\n--- 场景: %s ---\n', scenario_names{s});
        
        for a = 1:length(algorithms)
            fprintf('测试算法: %s\n', algorithm_names{a});
            
            for run = 1:num_runs
                fprintf('  运行 %d/%d...', run, num_runs);
                
                % 创建环境
                env = create_aoi_environment(scenarios{s});
                
                % 运行算法并计算AoI和能耗
                [aoi, energy] = run_aoi_algorithm(env, algorithms{a}, scenarios{s});
                
                aoi_results(a, s, run) = aoi;
                energy_results(a, s, run) = energy;
                
                fprintf(' AoI=%.2f ms, 能耗=%.3f mJ\n', aoi, energy*1000);
            end
            
            % 计算统计结果
            avg_aoi = mean(aoi_results(a, s, :));
            avg_energy = mean(energy_results(a, s, :));
            fprintf('  平均结果: AoI=%.2f ms, 能耗=%.3f mJ\n', avg_aoi, avg_energy*1000);
        end
    end
    
    % 保存结果
    results = struct();
    results.aoi_results = aoi_results;
    results.energy_results = energy_results;
    results.algorithms = algorithms;
    results.algorithm_names = algorithm_names;
    results.scenarios = scenarios;
    results.scenario_names = scenario_names;
    
    save('aoi_comparison_results.mat', 'results');
    
    % 生成可视化
    generate_aoi_pareto_plot(results);
    generate_scenario_comparison_plots(results);
    
    % 生成详细报告
    generate_aoi_analysis_report(results);
    
    fprintf('\n=== AoI对比实验完成 ===\n');
    fprintf('结果已保存到 aoi_comparison_results.mat\n');
end

function env = create_aoi_environment(scenario_type)
    % 创建AoI实验环境
    
    env = struct();
    env.max_steps = 1000;
    env.power_levels = [5, 10, 15, 20, 25, 30]; % mW
    env.action_dim = length(env.power_levels);
    env.state_dim = 6;
    env.current_step = 1;
    
    % 生成场景特定的数据
    switch scenario_type
        case 'static'
            % 静态监测场景：低运动强度，稳定信道
            env.motion_intensity = 0.05 + 0.02 * randn(1, env.max_steps);
            env.motion_intensity = max(0, env.motion_intensity);
            env.packet_generation_rate = 1.0; % 每秒1个包
            
        case 'dynamic'
            % 动态转换场景：运动强度突变
            transition_point = round(env.max_steps * 0.6);
            env.motion_intensity = [0.1 * ones(1, transition_point), ...
                                   2.0 * ones(1, env.max_steps - transition_point)];
            env.motion_intensity = env.motion_intensity + 0.1 * randn(1, env.max_steps);
            env.motion_intensity = max(0, env.motion_intensity);
            env.packet_generation_rate = 1.5; % 动态场景数据量更大
            
        case 'periodic'
            % 周期性运动场景：周期性运动强度
            t = 1:env.max_steps;
            env.motion_intensity = 1.0 + 0.8 * sin(2*pi*t/50) + 0.1 * randn(1, env.max_steps);
            env.motion_intensity = max(0, env.motion_intensity);
            env.packet_generation_rate = 1.2; % 中等数据量
    end
    
    % 生成信道质量数据（基于运动强度）
    env.channel_quality = -60 - 15 * env.motion_intensity - 5 * randn(1, env.max_steps);
    env.channel_quality = max(-90, min(-40, env.channel_quality));
    
    % AoI相关参数
    env.last_successful_transmission = zeros(1, env.max_steps); % 上次成功传输时间
    env.packet_timestamps = zeros(1, env.max_steps); % 包生成时间戳
    
    % 初始化包生成时间戳
    for i = 1:env.max_steps
        env.packet_timestamps(i) = i / env.packet_generation_rate;
    end
end

function [avg_aoi, total_energy] = run_aoi_algorithm(env, algorithm, scenario_type)
    % 运行指定算法并计算AoI和能耗
    
    switch algorithm
        case 'hierarchical_rl'
            [avg_aoi, total_energy] = run_hierarchical_rl_aoi(env, scenario_type);
        case 'dqn'
            [avg_aoi, total_energy] = run_dqn_aoi(env, scenario_type);
        case 'actor_critic'
            [avg_aoi, total_energy] = run_actor_critic_aoi(env, scenario_type);
        otherwise
            error('未知算法: %s', algorithm);
    end
end

function [avg_aoi, total_energy] = run_hierarchical_rl_aoi(env, scenario_type)
    % 分层RL算法的AoI实现
    
    % 创建分层RL智能体
    agent = create_hierarchical_agent_aoi(env, scenario_type);
    
    total_energy = 0;
    aoi_values = [];
    last_successful_time = 0;
    
    for step = 1:env.max_steps
        % 获取当前状态
        state = get_current_state(env, step);
        
        % 分层决策
        meta_action = select_meta_action(agent, state, scenario_type);
        action = select_local_action(agent, state, meta_action, scenario_type);
        
        % 执行动作
        power = env.power_levels(action);
        [success, energy] = execute_transmission(env, step, power);
        
        total_energy = total_energy + energy;
        
        % 计算AoI
        if success
            last_successful_time = step;
        end
        
        current_aoi = step - last_successful_time;
        aoi_values = [aoi_values, current_aoi];
        
        % 更新智能体（简化版本）
        reward = calculate_aoi_reward(current_aoi, energy, scenario_type);
        update_hierarchical_agent(agent, state, action, reward);
    end
    
    avg_aoi = mean(aoi_values);
end

function [avg_aoi, total_energy] = run_dqn_aoi(env, scenario_type)
    % 标准DQN算法的AoI实现
    
    % 创建DQN智能体
    agent = create_dqn_agent_aoi(env, scenario_type);
    
    total_energy = 0;
    aoi_values = [];
    last_successful_time = 0;
    
    for step = 1:env.max_steps
        % 获取当前状态
        state = get_current_state(env, step);
        
        % DQN决策
        action = select_dqn_action(agent, state, step);
        
        % 执行动作
        power = env.power_levels(action);
        [success, energy] = execute_transmission(env, step, power);
        
        total_energy = total_energy + energy;
        
        % 计算AoI
        if success
            last_successful_time = step;
        end
        
        current_aoi = step - last_successful_time;
        aoi_values = [aoi_values, current_aoi];
        
        % 更新智能体
        reward = calculate_aoi_reward(current_aoi, energy, scenario_type);
        update_dqn_agent(agent, state, action, reward);
    end
    
    avg_aoi = mean(aoi_values);
end

function [avg_aoi, total_energy] = run_actor_critic_aoi(env, scenario_type)
    % 演员-评论家算法的AoI实现
    
    % 创建演员-评论家智能体
    agent = create_actor_critic_agent_aoi(env, scenario_type);
    
    total_energy = 0;
    aoi_values = [];
    last_successful_time = 0;
    
    for step = 1:env.max_steps
        % 获取当前状态
        state = get_current_state(env, step);
        
        % 演员-评论家决策
        action = select_actor_critic_action(agent, state);
        
        % 执行动作
        power = env.power_levels(action);
        [success, energy] = execute_transmission(env, step, power);
        
        total_energy = total_energy + energy;
        
        % 计算AoI
        if success
            last_successful_time = step;
        end
        
        current_aoi = step - last_successful_time;
        aoi_values = [aoi_values, current_aoi];
        
        % 更新智能体
        reward = calculate_aoi_reward(current_aoi, energy, scenario_type);
        update_actor_critic_agent(agent, state, action, reward);
    end
    
    avg_aoi = mean(aoi_values);
end

function state = get_current_state(env, step)
    % 获取当前状态向量
    
    motion = env.motion_intensity(step);
    channel = env.channel_quality(step);
    
    % 构建状态向量
    state = [
        motion;                    % 运动强度
        channel;                   % 信道质量
        step / env.max_steps;      % 时间进度
        randn();                   % 噪声1
        randn();                   % 噪声2
        randn()                    % 噪声3
    ];
end

function [success, energy] = execute_transmission(env, step, power)
    % 执行传输并返回成功状态和能耗
    
    % 计算传输成功概率
    channel_quality = env.channel_quality(step);
    snr = power + channel_quality + 90; % 假设噪声底-90dBm
    
    % 基于SNR计算成功概率
    if snr > 20
        success_prob = 0.95;
    elseif snr > 10
        success_prob = 0.7 + 0.25 * (snr - 10) / 10;
    elseif snr > 0
        success_prob = 0.3 + 0.4 * snr / 10;
    else
        success_prob = 0.1 + 0.2 * max(0, (snr + 10) / 10);
    end
    
    % 随机判断传输是否成功
    success = rand() < success_prob;
    
    % 计算能耗（包括传输和处理能耗）
    transmission_energy = power * 1e-3 * 0.001; % 1ms传输时间
    processing_energy = 0.5e-6; % 固定处理能耗
    energy = transmission_energy + processing_energy;
end

function reward = calculate_aoi_reward(aoi, energy, scenario_type)
    % 计算基于AoI和能耗的奖励函数

    % AoI惩罚（AoI越大惩罚越大）
    aoi_penalty = -aoi * 0.1;

    % 能耗惩罚
    energy_penalty = -energy * 1000;

    % 根据场景调整权重
    switch scenario_type
        case 'static'
            % 静态场景：更注重能耗
            reward = 0.3 * aoi_penalty + 0.7 * energy_penalty;
        case 'dynamic'
            % 动态场景：平衡AoI和能耗
            reward = 0.5 * aoi_penalty + 0.5 * energy_penalty;
        case 'periodic'
            % 周期性场景：更注重AoI
            reward = 0.7 * aoi_penalty + 0.3 * energy_penalty;
    end
end

% ========== 智能体创建和更新函数 ==========

function agent = create_hierarchical_agent_aoi(env, scenario_type)
    % 创建分层RL智能体

    agent = struct();
    agent.meta_q_table = randn(10, 4) * 0.01;
    agent.local_q_table = randn(10, env.action_dim) * 0.01;
    agent.scenario_type = scenario_type;

    % 根据场景调整参数
    switch scenario_type
        case 'static'
            agent.meta_epsilon = 0.1;
            agent.local_epsilon = 0.1;
            agent.learning_rate = 0.01;
        case 'dynamic'
            agent.meta_epsilon = 0.3;
            agent.local_epsilon = 0.3;
            agent.learning_rate = 0.02;
        case 'periodic'
            agent.meta_epsilon = 0.2;
            agent.local_epsilon = 0.2;
            agent.learning_rate = 0.015;
    end

    agent.gamma = 0.95;
end

function agent = create_dqn_agent_aoi(env, scenario_type)
    % 创建DQN智能体

    agent = struct();
    agent.q_table = randn(10, env.action_dim) * 0.01;
    agent.scenario_type = scenario_type;

    % 根据场景调整参数
    switch scenario_type
        case 'static'
            agent.epsilon = 0.2;
            agent.epsilon_decay = 0.995;
            agent.learning_rate = 0.01;
        case 'dynamic'
            agent.epsilon = 0.4;
            agent.epsilon_decay = 0.99;
            agent.learning_rate = 0.02;
        case 'periodic'
            agent.epsilon = 0.3;
            agent.epsilon_decay = 0.992;
            agent.learning_rate = 0.015;
    end

    agent.gamma = 0.95;
    agent.min_epsilon = 0.01;
end

function agent = create_actor_critic_agent_aoi(env, scenario_type)
    % 创建演员-评论家智能体

    agent = struct();
    agent.actor_weights = randn(env.state_dim, env.action_dim) * 0.1;
    agent.critic_weights = randn(env.state_dim, 1) * 0.1;
    agent.scenario_type = scenario_type;

    % 根据场景调整参数
    switch scenario_type
        case 'static'
            agent.actor_lr = 0.001;
            agent.critic_lr = 0.002;
        case 'dynamic'
            agent.actor_lr = 0.002;
            agent.critic_lr = 0.004;
        case 'periodic'
            agent.actor_lr = 0.0015;
            agent.critic_lr = 0.003;
    end

    agent.gamma = 0.95;
end

function meta_action = select_meta_action(agent, state, scenario_type)
    % 分层RL上层动作选择

    if rand() < agent.meta_epsilon
        % 探索：根据场景生成策略
        switch scenario_type
            case 'static'
                meta_action = [0.8; 0.2; 0.7; 0.3]; % 节能优先
            case 'dynamic'
                meta_action = [0.5; 0.5; 0.4; 0.6]; % 平衡策略
            case 'periodic'
                meta_action = [0.6; 0.4; 0.5; 0.5]; % 适中策略
        end
    else
        % 利用：基于Q表选择
        state_idx = discretize_state(state);
        [~, best_meta] = max(agent.meta_q_table(state_idx, :));

        switch best_meta
            case 1
                meta_action = [0.8; 0.2; 0.7; 0.3]; % 节能优先
            case 2
                meta_action = [0.6; 0.4; 0.5; 0.5]; % 平衡
            case 3
                meta_action = [0.4; 0.6; 0.3; 0.7]; % 性能优先
            case 4
                meta_action = [0.5; 0.5; 0.4; 0.6]; % 自适应
        end
    end
end

function action = select_local_action(agent, state, meta_action, scenario_type)
    % 分层RL下层动作选择

    if rand() < agent.local_epsilon
        % 探索：基于meta策略指导
        energy_priority = meta_action(1);

        % 根据场景和策略选择动作概率
        switch scenario_type
            case 'static'
                action_probs = [0.6; 0.25; 0.1; 0.04; 0.01; 0] * energy_priority;
            case 'dynamic'
                action_probs = [0.2; 0.3; 0.25; 0.15; 0.08; 0.02];
            case 'periodic'
                action_probs = [0.3; 0.3; 0.2; 0.15; 0.04; 0.01];
        end

        action_probs = action_probs / sum(action_probs);
        action = randsample(length(action_probs), 1, true, action_probs);
    else
        % 利用：基于Q表选择
        state_idx = discretize_state(state);
        [~, action] = max(agent.local_q_table(state_idx, :));
    end
end

function action = select_dqn_action(agent, state, step)
    % DQN动作选择

    % 衰减探索率
    current_epsilon = max(agent.min_epsilon, agent.epsilon * (agent.epsilon_decay ^ step));

    if rand() < current_epsilon
        % 探索：随机选择
        action = randi(size(agent.q_table, 2));
    else
        % 利用：选择最优动作
        state_idx = discretize_state(state);
        [~, action] = max(agent.q_table(state_idx, :));
    end
end

function action = select_actor_critic_action(agent, state)
    % 演员-评论家动作选择

    % 计算动作概率
    action_logits = agent.actor_weights' * state;
    action_probs = softmax_stable(action_logits);

    % 根据概率分布选择动作
    action = randsample(length(action_probs), 1, true, action_probs);
end

% ========== 智能体更新函数 ==========

function update_hierarchical_agent(agent, state, action, reward)
    % 更新分层RL智能体

    state_idx = discretize_state(state);

    % 更新本地Q表
    old_q = agent.local_q_table(state_idx, action);
    agent.local_q_table(state_idx, action) = old_q + agent.learning_rate * (reward - old_q);

    % 简化的元策略更新
    meta_idx = mod(state_idx, 4) + 1;
    old_meta_q = agent.meta_q_table(state_idx, meta_idx);
    agent.meta_q_table(state_idx, meta_idx) = old_meta_q + agent.learning_rate * (reward - old_meta_q);
end

function update_dqn_agent(agent, state, action, reward)
    % 更新DQN智能体

    state_idx = discretize_state(state);

    % Q学习更新
    old_q = agent.q_table(state_idx, action);
    agent.q_table(state_idx, action) = old_q + agent.learning_rate * (reward - old_q);
end

function update_actor_critic_agent(agent, state, action, reward)
    % 更新演员-评论家智能体

    % 计算状态价值
    state_value = agent.critic_weights' * state;
    td_error = reward - state_value;

    % 更新评论家
    agent.critic_weights = agent.critic_weights + agent.critic_lr * td_error * state;

    % 更新演员
    action_logits = agent.actor_weights' * state;
    action_probs = softmax_stable(action_logits);

    % 策略梯度更新
    grad = zeros(size(agent.actor_weights));
    grad(:, action) = td_error * state;
    agent.actor_weights = agent.actor_weights + agent.actor_lr * grad;
end

% ========== 辅助函数 ==========

function state_idx = discretize_state(state)
    % 将连续状态离散化为索引

    % 简化的状态离散化
    motion = max(1, min(10, round(state(1) * 5 + 5)));
    channel = max(1, min(10, round((state(2) + 90) / 10)));

    state_idx = motion; % 简化为仅使用运动强度
end

function probs = softmax_stable(x)
    % 数值稳定的Softmax函数

    exp_x = exp(x - max(x));
    probs = exp_x / sum(exp_x);
end

% ========== 可视化函数 ==========

function generate_aoi_pareto_plot(results)
    % 生成AoI-能耗Pareto前沿图

    figure('Position', [100, 100, 1200, 400]);

    % 计算平均结果
    avg_aoi = squeeze(mean(results.aoi_results, 3));
    avg_energy = squeeze(mean(results.energy_results, 3)) * 1000; % 转换为mJ

    colors = [0.2, 0.6, 0.8; 0.8, 0.4, 0.2; 0.4, 0.8, 0.4]; % 蓝、橙、绿
    markers = {'o', 's', '^'};

    for s = 1:length(results.scenarios)
        subplot(1, 3, s);
        hold on;

        for a = 1:length(results.algorithms)
            scatter(avg_energy(a, s), avg_aoi(a, s), 100, colors(a, :), ...
                   'Marker', markers{a}, 'LineWidth', 2, 'DisplayName', results.algorithm_names{a});
        end

        xlabel('平均能耗 (mJ)');
        ylabel('平均AoI (ms)');
        title(sprintf('%s场景', results.scenario_names{s}));
        legend('Location', 'best');
        grid on;

        % 添加Pareto前沿线
        [sorted_energy, idx] = sort(avg_energy(:, s));
        sorted_aoi = avg_aoi(idx, s);
        plot(sorted_energy, sorted_aoi, 'k--', 'Alpha', 0.5, 'LineWidth', 1);
    end

    sgtitle('AoI-能耗 Pareto前沿图', 'FontSize', 16, 'FontWeight', 'bold');

    % 保存图片
    saveas(gcf, 'aoi_pareto_comparison.png');
    saveas(gcf, 'aoi_pareto_comparison.fig');

    fprintf('AoI-能耗Pareto前沿图已保存\n');
end

function generate_scenario_comparison_plots(results)
    % 生成三种场景下的详细对比图

    % 计算统计结果
    avg_aoi = squeeze(mean(results.aoi_results, 3));
    std_aoi = squeeze(std(results.aoi_results, 0, 3));
    avg_energy = squeeze(mean(results.energy_results, 3)) * 1000;
    std_energy = squeeze(std(results.energy_results, 0, 3)) * 1000;

    % AoI对比图
    figure('Position', [100, 200, 800, 600]);

    subplot(2, 1, 1);
    bar_data = avg_aoi';
    bar_handle = bar(bar_data);

    % 设置颜色
    colors = [0.2, 0.6, 0.8; 0.8, 0.4, 0.2; 0.4, 0.8, 0.4];
    for i = 1:length(bar_handle)
        bar_handle(i).FaceColor = colors(i, :);
    end

    hold on;
    % 添加误差棒
    x_pos = [];
    for i = 1:size(bar_data, 1)
        x_pos = [x_pos; (1:size(bar_data, 2)) + (i-2)*0.25];
    end
    errorbar(x_pos(:), bar_data(:), std_aoi'(:), 'k.', 'LineWidth', 1);

    xlabel('场景');
    ylabel('平均AoI (ms)');
    title('三种场景下的平均信息年龄对比');
    legend(results.algorithm_names, 'Location', 'best');
    set(gca, 'XTickLabel', results.scenario_names);
    grid on;

    % 能耗对比图
    subplot(2, 1, 2);
    bar_data = avg_energy';
    bar_handle = bar(bar_data);

    % 设置颜色
    for i = 1:length(bar_handle)
        bar_handle(i).FaceColor = colors(i, :);
    end

    hold on;
    % 添加误差棒
    errorbar(x_pos(:), bar_data(:), std_energy'(:), 'k.', 'LineWidth', 1);

    xlabel('场景');
    ylabel('平均能耗 (mJ)');
    title('三种场景下的平均能耗对比');
    legend(results.algorithm_names, 'Location', 'best');
    set(gca, 'XTickLabel', results.scenario_names);
    grid on;

    % 保存图片
    saveas(gcf, 'aoi_scenario_comparison.png');
    saveas(gcf, 'aoi_scenario_comparison.fig');

    fprintf('场景对比图已保存\n');
end

function generate_aoi_analysis_report(results)
    % 生成详细的AoI分析报告

    fprintf('\n=== AoI对比实验分析报告 ===\n');

    % 计算统计结果
    avg_aoi = squeeze(mean(results.aoi_results, 3));
    std_aoi = squeeze(std(results.aoi_results, 0, 3));
    avg_energy = squeeze(mean(results.energy_results, 3)) * 1000;
    std_energy = squeeze(std(results.energy_results, 0, 3)) * 1000;

    % 1. 总体性能分析
    fprintf('\n1. 总体性能分析:\n');
    fprintf('%-15s %-12s %-12s %-12s\n', '算法', '平均AoI(ms)', '平均能耗(mJ)', 'AoI×能耗');
    fprintf('%-15s %-12s %-12s %-12s\n', '----', '----------', '----------', '--------');

    for a = 1:length(results.algorithms)
        overall_aoi = mean(avg_aoi(a, :));
        overall_energy = mean(avg_energy(a, :));
        aoi_energy_product = overall_aoi * overall_energy;

        fprintf('%-15s %-12.2f %-12.3f %-12.1f\n', ...
               results.algorithm_names{a}, overall_aoi, overall_energy, aoi_energy_product);
    end

    % 2. 场景特定分析
    fprintf('\n2. 场景特定分析:\n');

    for s = 1:length(results.scenarios)
        fprintf('\n--- %s场景 ---\n', results.scenario_names{s});
        fprintf('%-15s %-15s %-15s\n', '算法', 'AoI (ms)', '能耗 (mJ)');
        fprintf('%-15s %-15s %-15s\n', '----', '--------', '--------');

        for a = 1:length(results.algorithms)
            fprintf('%-15s %-7.2f±%-6.2f %-7.3f±%-6.3f\n', ...
                   results.algorithm_names{a}, ...
                   avg_aoi(a, s), std_aoi(a, s), ...
                   avg_energy(a, s), std_energy(a, s));
        end

        % 找出最佳算法
        [min_aoi, best_aoi_idx] = min(avg_aoi(:, s));
        [min_energy, best_energy_idx] = min(avg_energy(:, s));

        fprintf('  最低AoI: %s (%.2f ms)\n', results.algorithm_names{best_aoi_idx}, min_aoi);
        fprintf('  最低能耗: %s (%.3f mJ)\n', results.algorithm_names{best_energy_idx}, min_energy);
    end

    % 3. Pareto效率分析
    fprintf('\n3. Pareto效率分析:\n');

    for s = 1:length(results.scenarios)
        fprintf('\n--- %s场景Pareto分析 ---\n', results.scenario_names{s});

        scenario_aoi = avg_aoi(:, s);
        scenario_energy = avg_energy(:, s);

        % 计算Pareto前沿
        pareto_indices = find_pareto_front(scenario_aoi, scenario_energy);

        fprintf('Pareto最优算法: ');
        for i = 1:length(pareto_indices)
            fprintf('%s ', results.algorithm_names{pareto_indices(i)});
        end
        fprintf('\n');

        % 计算支配关系
        for a1 = 1:length(results.algorithms)
            dominated_by = [];
            for a2 = 1:length(results.algorithms)
                if a1 ~= a2
                    if scenario_aoi(a2) <= scenario_aoi(a1) && scenario_energy(a2) <= scenario_energy(a1) && ...
                       (scenario_aoi(a2) < scenario_aoi(a1) || scenario_energy(a2) < scenario_energy(a1))
                        dominated_by = [dominated_by, a2];
                    end
                end
            end

            if ~isempty(dominated_by)
                fprintf('  %s被以下算法支配: ', results.algorithm_names{a1});
                for i = 1:length(dominated_by)
                    fprintf('%s ', results.algorithm_names{dominated_by(i)});
                end
                fprintf('\n');
            end
        end
    end

    % 4. 算法特性分析
    fprintf('\n4. 算法特性分析:\n');

    % 计算变异系数
    fprintf('\n稳定性分析 (变异系数):\n');
    fprintf('%-15s %-12s %-12s\n', '算法', 'AoI稳定性', '能耗稳定性');
    fprintf('%-15s %-12s %-12s\n', '----', '--------', '--------');

    for a = 1:length(results.algorithms)
        aoi_cv = std(avg_aoi(a, :)) / mean(avg_aoi(a, :)) * 100;
        energy_cv = std(avg_energy(a, :)) / mean(avg_energy(a, :)) * 100;

        fprintf('%-15s %-12.1f%% %-12.1f%%\n', ...
               results.algorithm_names{a}, aoi_cv, energy_cv);
    end

    % 5. 实验结论
    fprintf('\n5. 实验结论:\n');

    % 找出总体最佳算法
    overall_aoi_avg = mean(avg_aoi, 2);
    overall_energy_avg = mean(avg_energy, 2);

    [~, best_aoi_overall] = min(overall_aoi_avg);
    [~, best_energy_overall] = min(overall_energy_avg);

    fprintf('✓ 最低平均AoI: %s (%.2f ms)\n', ...
           results.algorithm_names{best_aoi_overall}, overall_aoi_avg(best_aoi_overall));
    fprintf('✓ 最低平均能耗: %s (%.3f mJ)\n', ...
           results.algorithm_names{best_energy_overall}, overall_energy_avg(best_energy_overall));

    % 场景适应性分析
    fprintf('\n场景适应性排名:\n');

    % 计算每个算法在不同场景下的性能一致性
    consistency_scores = zeros(length(results.algorithms), 1);
    for a = 1:length(results.algorithms)
        % 使用AoI和能耗的归一化方差作为一致性指标
        aoi_norm_var = var(avg_aoi(a, :)) / mean(avg_aoi(a, :))^2;
        energy_norm_var = var(avg_energy(a, :)) / mean(avg_energy(a, :))^2;
        consistency_scores(a) = 1 / (1 + aoi_norm_var + energy_norm_var);
    end

    [~, consistency_rank] = sort(consistency_scores, 'descend');

    for i = 1:length(consistency_rank)
        fprintf('%d. %s (一致性得分: %.3f)\n', ...
               i, results.algorithm_names{consistency_rank(i)}, consistency_scores(consistency_rank(i)));
    end

    fprintf('\n=== 报告生成完成 ===\n');

    % 保存报告到文件
    diary('aoi_analysis_report.txt');
    generate_aoi_analysis_report(results);
    diary off;

    fprintf('详细报告已保存到 aoi_analysis_report.txt\n');
end

function pareto_indices = find_pareto_front(aoi_values, energy_values)
    % 找出Pareto前沿上的点

    n = length(aoi_values);
    pareto_indices = [];

    for i = 1:n
        is_pareto = true;
        for j = 1:n
            if i ~= j
                % 如果点j支配点i，则点i不在Pareto前沿上
                if aoi_values(j) <= aoi_values(i) && energy_values(j) <= energy_values(i) && ...
                   (aoi_values(j) < aoi_values(i) || energy_values(j) < energy_values(i))
                    is_pareto = false;
                    break;
                end
            end
        end

        if is_pareto
            pareto_indices = [pareto_indices, i];
        end
    end
end
