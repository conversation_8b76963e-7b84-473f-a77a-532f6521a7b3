% 测试消融实验的简化版本
% 验证三个消融实验是否能正常运行

clear; clc;
addpath(genpath('.'));

fprintf('=== 消融实验验证测试 ===\n');

% 场景示例 (静态)
scenario = struct('name','静态测试','type','static');

% 基础参数配置 (简化版，快速测试)
base_params = struct(...
    'num_episodes',10,...           % 减少到10轮快速测试
    'max_steps_per_episode',50,...  % 减少步数
    'batch_size',16,...
    'update_frequency',5,...
    'learning_rate',0.001,...
    'gamma',0.95,...
    'epsilon',1.0,...
    'epsilon_decay',0.99,...
    'epsilon_min',0.1);

try
    %% 测试1) 仅下层 DQN vs 分层 RL
    fprintf('\n=== 测试1: 下层 DQN vs 分层 RL ===\n');
    env1 = rl_environment();
    fprintf('环境创建成功，状态维度: %d, 动作维度: %d\n', env1.state_dim, env1.action_dim);
    
    % 测试DQN
    fprintf('测试DQN训练...\n');
    [agent_dqn, res_dqn] = train_dqn(env1, base_params);
    fprintf('DQN训练完成，平均奖励: %.2f\n', res_dqn.final_avg_reward);
    
    % 测试分层RL
    fprintf('测试分层RL训练...\n');
    env2 = rl_environment();
    [agent_hrl, res_hrl] = train_hierarchical_rl(env2, scenario);
    fprintf('分层RL训练完成，平均奖励: %.2f\n', res_hrl.final_avg_reward);
    
    fprintf('✓ 测试1通过: DQN %.2f vs 分层RL %.2f\n', res_dqn.final_avg_reward, res_hrl.final_avg_reward);
    
    %% 测试2) 不同 Manager hidden size
    fprintf('\n=== 测试2: 不同 Manager hidden size ===\n');
    hsizes = [32, 64];  % 简化测试，只测试两个尺寸
    results_h = [];
    
    for h = hsizes
        fprintf('测试隐藏层大小: %d\n', h);
        env_h = rl_environment();
        
        % 设置隐藏层大小
        evalin('base', sprintf('HRL_HIDDEN_SIZE = %d;', h));
        [~, res_h] = train_hierarchical_rl(env_h, scenario);
        results_h(end+1) = res_h.final_avg_reward;
        fprintf('Hidden %d -> 平均奖励: %.2f\n', h, res_h.final_avg_reward);
    end
    
    % 清理变量
    if evalin('base', 'exist("HRL_HIDDEN_SIZE", "var")')
        evalin('base', 'clear HRL_HIDDEN_SIZE');
    end
    
    fprintf('✓ 测试2通过: 不同隐藏层大小测试完成\n');
    
    %% 测试3) 无 RSSI / 无 IMU 特征
    fprintf('\n=== 测试3: 特征遮蔽 ===\n');
    
    % 测试无RSSI
    fprintf('测试无RSSI特征...\n');
    env_no_rssi = make_mask_env('rssi');
    [~, res_rssi] = train_hierarchical_rl(env_no_rssi, scenario);
    fprintf('无RSSI平均奖励: %.2f\n', res_rssi.final_avg_reward);
    
    % 测试无IMU
    fprintf('测试无IMU特征...\n');
    env_no_imu = make_mask_env('imu');
    [~, res_imu] = train_hierarchical_rl(env_no_imu, scenario);
    fprintf('无IMU平均奖励: %.2f\n', res_imu.final_avg_reward);
    
    fprintf('✓ 测试3通过: 特征遮蔽测试完成\n');
    
    %% 总结
    fprintf('\n=== 消融实验验证总结 ===\n');
    fprintf('1. DQN vs 分层RL: %.2f vs %.2f\n', res_dqn.final_avg_reward, res_hrl.final_avg_reward);
    fprintf('2. 不同隐藏层: ');
    for i = 1:length(hsizes)
        fprintf('%d->%.1f ', hsizes(i), results_h(i));
    end
    fprintf('\n');
    fprintf('3. 特征遮蔽: 无RSSI %.2f, 无IMU %.2f, 原始 %.2f\n', ...
            res_rssi.final_avg_reward, res_imu.final_avg_reward, res_hrl.final_avg_reward);
    
    fprintf('\n✅ 所有消融实验验证通过！\n');
    
catch ME
    fprintf('\n❌ 测试失败: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    rethrow(ME);
end

%% 辅助函数
function env_mask = make_mask_env(flag)
    % 使用函数版本的环境（来自adaptation_environment_interface.m）
    base_env = rl_environment();
    env_mask = base_env; % value copy

    % 保存原始函数
    original_reset = base_env.reset;
    original_step = base_env.step;
    original_get_state = base_env.get_current_state;

    % 定义新的状态获取函数
    function s = new_get_state()
        s = original_get_state();
        % 根据标志遮蔽特征
        switch flag
            case 'rssi'
                if length(s) >= 4
                    s(4) = 0; % 遮蔽RSSI特征
                end
            case 'imu'
                if length(s) >= 1
                    s(1) = 0; % 遮蔽IMU特征1
                end
                if length(s) >= 5
                    s(5) = 0; % 遮蔽IMU特征2
                end
        end
    end
    
    % 重新定义环境函数
    function s = new_reset()
        s = original_reset();
        s = new_get_state();
    end
    
    function [next_state, reward, done, info] = new_step(action)
        [next_state, reward, done, info] = original_step(action);
        next_state = new_get_state();
    end

    % 更新环境函数
    env_mask.get_current_state = @new_get_state;
    env_mask.reset = @new_reset;
    env_mask.step = @new_step;
end
