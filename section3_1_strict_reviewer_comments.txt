《问题复杂度分析与分层必要性论证》严格审稿意见

## 总体评价：MAJOR REVISION REQUIRED

虽然作者在数学建模方面有所改进，但当前版本仍存在多个根本性的学术问题，需要大幅修改才能达到发表标准。

## 质疑1：状态空间建模仍然存在概念混乱和不一致性

**严重问题：**
1. **前后不一致**：第5行声称状态空间为2.4×10^8，但第7行的样本复杂度计算又基于这个数值，然而第11行突然改为分层后的状态空间144和48，缺乏逻辑连贯性。

2. **信道模式定义模糊**：作者声称"12种典型信道模式"但未提供任何理论依据或实验支撑。WBAN信道建模通常基于路径损耗模型，不是简单的"模式分类"问题。

3. **空间相关性处理错误**：声称"节点间存在强烈的空间相关性，不能假设独立"，但随后的建模中仍然使用了L_E^N的独立假设，自相矛盾。

**改进要求：**
- 提供信道模式分类的理论依据和实验验证
- 明确说明如何处理节点间的空间相关性
- 保持状态空间定义的前后一致性

## 质疑2：样本复杂度理论引用错误且计算有误

**严重问题：**
1. **理论引用错误**：Strehl和Littman (2008)的结果是O(|S||A|log(1/δ)/(ε²(1-γ)²))，但经典的PAC-MDP界通常包含混合时间等额外因子，作者的引用过于简化。

2. **参数设置不合理**：
   - γ=0.9对应的不是"100ms决策周期"，折扣因子与时间周期的关系是γ = e^(-r×Δt)，需要明确衰减率r
   - ε=0.05的精度要求过于严格，实际应用中通常使用ε=0.1-0.2
   - 动作空间|A|=8^8假设所有节点同时决策，但WBAN通常采用TDMA，节点不是同时决策

3. **数值计算错误**：7.4×10^19这个数值的计算过程存在数量级错误，实际应该更小。

**改进要求：**
- 使用更准确的PAC-MDP理论界
- 提供合理的参数设置依据
- 重新计算并验证数值结果

## 质疑3：分层分解的理论基础薄弱且存在逻辑漏洞

**严重问题：**
1. **Options框架应用不当**：作者引用Options框架但未说明如何定义termination condition和initiation set，这是Options的核心要素。

2. **状态空间分解不合理**：
   - 高层状态"网络整体能耗水平"如何从单个节点状态聚合？缺乏明确定义
   - "QoS需求等级"是外部输入还是系统状态？概念混乱
   - 低层状态空间48维通过"参数共享"的说法缺乏理论支撑

3. **复杂度分析过于乐观**：从O(|S||A|)到O(|S_high||A_high| + |S_low||A_low|)的简化忽略了层间协调的复杂度。

**改进要求：**
- 明确定义Options的完整结构
- 提供状态聚合和分解的数学公式
- 分析层间协调的额外复杂度

## 质疑4：时间尺度分离分析缺乏定量支撑且存在矛盾

**严重问题：**
1. **实验数据引用不当**：Cavallari等人(2014)的研究是针对一般WBAN信道，不是功率控制场景，直接应用其结论是不合适的。

2. **耦合系数定义有问题**：
   - η = Cov(R_high, R_low)/√(Var(R_high)Var(R_low))这是相关系数，不是"耦合系数"
   - 阈值η < 0.3和η > 0.7缺乏理论依据，似乎是作者主观设定

3. **时间尺度分析矛盾**：一方面说"2个数量级差异"，另一方面又说快速运动时"减小到1个数量级以内"，这种变化范围过大，质疑分层方法的鲁棒性。

**改进要求：**
- 提供WBAN功率控制场景下的实际时间尺度测量数据
- 给出耦合系数阈值的理论推导
- 分析时间尺度变化对分层效果的定量影响

## 质疑5：计算复杂度分析存在多处错误和不现实假设

**严重问题：**
1. **网络结构设计不合理**：
   - 单层网络输出维度8^8 ≈ 1.67×10^7是不现实的，没有任何实际的DQN会使用如此巨大的输出层
   - 实际的DQN通常使用dueling architecture或其他技巧来处理大动作空间

2. **硬件约束分析过于乐观**：
   - CC2650的20KB RAM根本无法支持作者描述的网络结构
   - "云边协同部署"的通信延迟和能耗开销被完全忽略

3. **对比分析不公平**：与凸优化、启发式算法的对比缺乏公平性，没有考虑这些方法在WBAN场景下的实际适用性。

**改进要求：**
- 设计现实可行的网络结构
- 提供详细的硬件资源预算分析
- 进行公平的算法对比

## 质疑6：结论过于绝对化且缺乏实验验证

**严重问题：**
1. **缺乏实验支撑**：整篇文章都是理论分析，没有任何实验数据支撑其结论。

2. **适用条件过于理想化**：作者承认分层方法只在"静态和慢速移动"场景下有效，但这严重限制了方法的实用价值。

3. **与现有工作对比不足**：缺乏与现有WBAN功率控制方法的详细对比。

**改进要求：**
- 提供仿真或实验数据验证理论分析
- 扩大方法的适用范围或明确其局限性
- 与现有方法进行全面对比

## 具体修改建议

### 1. 重新构建状态空间模型
- 基于WBAN的物理特性和协议约束，给出严格的状态空间定义
- 明确处理节点间空间相关性的方法
- 提供状态空间大小的准确计算

### 2. 修正样本复杂度分析
- 使用准确的PAC-MDP理论结果
- 提供合理的参数设置和计算过程
- 考虑WBAN特有的约束条件

### 3. 完善分层理论基础
- 明确定义Options的完整结构
- 提供状态分解的数学公式和理论依据
- 分析层间协调的复杂度

### 4. 加强实验验证
- 提供仿真实验验证理论分析
- 与现有方法进行公平对比
- 分析方法在不同场景下的性能

### 5. 客观评估方法局限性
- 明确方法的适用条件和局限性
- 分析失效场景和应对策略
- 避免过度夸大方法优势

## 总结

当前版本虽然在数学表述上有所改进，但仍存在多个根本性问题：理论基础不扎实、数值计算有误、实验验证缺失、结论过于绝对化。建议作者进行大幅修改，特别是加强理论分析的严谨性和实验验证的完整性。只有解决了这些问题，该工作才能达到发表标准。

**建议：MAJOR REVISION - 需要大幅修改后重新审稿**
