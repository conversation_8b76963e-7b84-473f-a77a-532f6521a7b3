function env = make_wban_env_from_pl(pl_db, scenario_type, dt)
% make_wban_env_from_pl  Build WBAN RL environment from measured pathloss
% Inputs:
%   pl_db        - vector of pathloss in dB
%   scenario_type- 'static' | 'dynamic' | 'periodic' (affects reward weights)
%   dt           - time step in seconds (default 0.1)
% Outputs:
%   env          - struct with fields/state and methods: reset(), step()
% Notes:
%   This recreates a subset of rl_environment() behavior without modifying
%   original files. Energy/PDR/Delay models mirror adaptation_environment_interface.m.

if nargin < 3 || isempty(dt), dt = 0.1; end
if nargin < 2, scenario_type = 'static'; end

T = numel(pl_db);
% -------- base fields --------
env = struct();
env.state_dim = 8;
env.action_dim = 4;
env.max_steps = T;
env.current_step = 1;
env.episode_step = 0;
env.time_vector = (0:T-1) * dt;

% power levels (mW)
env.power_levels = [10, 15, 20, 25];

% derive RSSI from pathloss
rssi = -pl_db(:) + randn(T,1); % small noise
rssi = max(-100, min(-40, rssi));
env.rssi_series = rssi;

% IMU proxy: use |diff(pl)| smoothed
imu = [0; abs(diff(pl_db(:)))];
imu = movmean(imu, 5);
imu = normalize_to_range(imu, 0.1, 2.0);
env.imu_series = imu;

% EMG/ECG proxies (bounded positives)
emg = 25 + 10*sin(2*pi*0.05*env.time_vector(:)) + 5*randn(T,1);
emg = max(0, emg);
ecq = 75 + 15*sin(2*pi*0.02*env.time_vector(:)) + 5*randn(T,1);
ecq = max(60, min(150, ecq));
env.emg_series = emg;
env.ecg_series = ecq;

% reward weights per scenario
switch lower(scenario_type)
    case 'static'
        env.energy_weight = 0.5; env.pdr_weight = 0.3; env.delay_weight = 0.2;
    case 'dynamic'
        env.energy_weight = 0.45; env.pdr_weight = 0.35; env.delay_weight = 0.20;
    case 'periodic'
        env.energy_weight = 0.48; env.pdr_weight = 0.32; env.delay_weight = 0.20;
    otherwise
        env.energy_weight = 0.5; env.pdr_weight = 0.3; env.delay_weight = 0.2;
end

% internal state snapshot
env.current_state = zeros(env.state_dim,1);

% method handles
env.reset = @() reset_env();
env.step  = @(action) step_env(action);

% cached last success time for AoI helper (not used internally)
env.last_successful_tx_time = 0;

disp('Custom WBAN env (from PL) created.');

    function state = reset_env()
        env.current_step = 1;
        env.episode_step = 0;
        state = get_state();
    end

    function state = get_state()
        k = env.current_step;
        % build 8-dim state as in original rl_environment
        state = zeros(env.state_dim,1);
        state(1) = env.imu_series(k);
        state(2) = env.emg_series(k);
        state(3) = env.ecg_series(k);
        state(4) = env.rssi_series(k);
        if k>1
            state(5) = env.imu_series(k-1);
            state(6) = env.emg_series(k-1);
        else
            state(5) = state(1);
            state(6) = state(2);
        end
        state(7) = k/env.max_steps;
        state(8) = env.episode_step/max(1,env.max_steps);
        env.current_state = state;
    end

    function [next_state, reward, done, info] = step_env(action)
        % clamp action
        action = max(1, min(env.action_dim, round(action)));
        power = env.power_levels(action);
        % compute PDR based on SNR
        k = env.current_step;
        current_rssi = env.rssi_series(k);
        noise_power = -90; % dBm
        signal_power = current_rssi + 10*log10(power);
        snr = signal_power - noise_power;
        if snr > 20
            pdr = 0.95 + 0.05*rand();
        elseif snr > 10
            pdr = 0.8 + 0.15*(snr-10)/10 + 0.05*rand();
        elseif snr > 0
            pdr = 0.5 + 0.3*snr/10 + 0.05*rand();
        else
            pdr = 0.1 + 0.4*max(0,(snr+10)/10) + 0.05*rand();
        end
        pdr = max(0, min(1, pdr));
        % delay
        motion_intensity = env.imu_series(k);
        base_delay = 10; % ms
        snr_delay = max(0, (20 - snr) * 2);
        motion_delay = motion_intensity * 5;
        delay = max(5, base_delay + snr_delay + motion_delay + 2*rand());
        % energy (mJ)
        transmission_time = dt; % s
        base_energy = power * transmission_time;
        processing_energy = 2 + rand();
        motion_energy = motion_intensity * 1;
        energy = max(1, base_energy + processing_energy + motion_energy);
        % reward
        energy_reward = -energy/50; pdr_reward = pdr*100; delay_reward = -delay/20;
        reward = env.energy_weight*energy_reward + env.pdr_weight*pdr_reward + env.delay_weight*delay_reward + 0.1*randn();

        % update counters
        env.current_step = env.current_step + 1;
        env.episode_step = env.episode_step + 1;
        done = env.current_step > env.max_steps;
        if ~done
            next_state = get_state();
        else
            next_state = env.current_state;
        end

        % info
        info = struct('energy',energy,'pdr',pdr,'delay',delay,'power',power,'action',action);
    end
end

function y = normalize_to_range(x, a, b)
    x = x(:);
    mn = min(x); mx = max(x);
    if mx - mn < 1e-9
        y = a*ones(size(x));
    else
        y = a + (x - mn) / (mx - mn) * (b - a);
    end
end

