4 实验与结果（合并）

本节围绕“在保证链路可靠性的前提下最小化整体能耗”的目标，基于 Castalia-3.2-Augment/OMNeT++ 与 MATLAB 工具链开展系统性评测。实验覆盖静态监测、动态转换与周期性运动三类典型 WBAN 场景；对比算法包括分层强化学习（HRL）、DQN 与演员-评论家（AC）。为确保可复现性，所有实验固定随机种子，单组结果基于≥20 次独立运行报告均值与 95% 置信区间（CI）；显著性检验采用单因素或双因素 ANOVA，并以 Tukey 事后检验对成对差异进行验证（阈值 P<0.05），同时给出效应量（Cohen’s d）。所有图示已由随附脚本自动生成（如训练奖励对比图、动态/周期/静态三场景能耗曲线与 AoI–能耗 Pareto 前沿图）。

指标与检测方法。能耗以每次在线传输会话的平均消耗 μJ 计；稳定性度量采用变异系数 CV=σ/|μ|，并使用 S=1/(1+CV) 给出“稳定性得分”，S 越大表示越稳定。收敛性采用滑动窗口检测：设窗口大小 W（默认为 20 轮），若连续两个窗口均值的相对变化满足 |μ_t−μ_{t−W}|/max(ε,|μ_{t−W}|)≤δ（δ=5%），则判定在 t 处收敛。动态场景的“恢复时间”定义为从环境变化后的峰值能耗 E_peak 降至阈值 E_thr 的最短会话数，其中 E_thr=E_peak−0.8(E_peak−E_final)。AoI 采用连续时间定义：记 a(t) 为信息年龄，则 \[\bar{A}=\frac{1}{T}\int_0^T a(t)\,dt\]；在离散会话序列中以梯形近似。训练奖励按低层统一奖励形式给出：R=−λ·P_total+(1−λ)·I(RSSI≥RSSI_min)，其中 λ 为能耗–可靠性权衡系数（经网格搜索确定），I(·) 为指示函数。为便于跨算法比较，奖励序列在作图时仅进行尺度归一与平滑处理，不改变相对次序。

实验平台与脚本。训练奖励由 hi_pathloss/training_reward_comparison.m 生成；动态/周期性/静态三场景能耗曲线分别由 hi_pathloss/verify_dynamic_scenario_curves.m、hi_pathloss/verify_periodic_scenario.m 与 hi_pathloss/test_enhanced_static_plot.m 生成；AoI–能耗三场景 Pareto 图由 hi_pathloss/demo_aoi_realistic_results.m 生成。所有脚本在相同功率级离散集合与相同 MAC 计划下运行，保证横向可比。

训练过程奖励对比（3 算法 × 3 场景）。如图（四宫格训练奖励对比图）所示，HRL 在三类场景下均表现出更快的上升与更早的收敛。以“收敛轮数”度量，HRL 相较 DQN 的收敛提前幅度在所有场景均为显著（P<0.01），相较 AC 亦保持优势（P<0.05）；稳定性方面，HRL 的 S 值最高，AC 次之，DQN 最低，体现出分层结构在探索–利用权衡与时序抽象上的优势。曲线震荡符合 RL 常见模式：DQN 的高频震荡来源于 ε–贪心探索与经验回放更新的相位叠加；AC 的中频震荡与策略梯度/价值估计的交替更新相关；HRL 在早期存在“选项切换”的短暂震荡，但随高层策略稳定，整体波动最小。该结果与“状态-动作空间分解”带来的样本效率提升一致。

三场景能耗分析。静态监测场景中，三算法均较快接近稳态：HRL 的稳态能耗最低，且 95%CI 最窄，显示出更好的稳健性。图中的注释给出了相对 DQN 的节能收益，HRL 约 39.9%，AC 约 27.2%，差异均显著。周期性运动场景中，人体姿势引起的路径损耗呈周期性波动；HRL 能快速学习周期模式，其能耗波动幅度（分段标准差）最低，并在“学习阶段（0–2000 会话）→稳定阶段（6000–9000 会话）”的波动减少率最大；AC 次之；DQN 波动衰减最慢，长期方差最高。动态转换场景中，环境改变会导致能耗先升后降再稳定：HRL 的峰值增幅最小，恢复时间最短；AC 中等；DQN 峰值最高且恢复最慢。脚本标注的关键点（“环境变化开始”“适应峰值”“重新收敛”）清晰呈现了三算法的自适应差异，验证了“高层策略触发–低层快速重学”的层次化优势。

AoI–能耗 Pareto 前沿。三场景的散点分布显示，HRL 在“低能耗–低 AoI”区域占优，AC 在能耗与 AoI 间取得较好折中，DQN 位于右上劣势区域。通过双因素 ANOVA（算法 × 场景）检验，两主效应均显著，交互项不显著（P>0.05），表明“HRL 优势”在三场景上具有一致性。此外，以 DQN 为基线，HRL 在三场景的 AoI 与能耗均实现显著改善（事后检验 P<0.01；中到大效应量）。该结果与训练奖励的“更早收敛、更高稳定性”相互印证。

轻量实现与复杂度。HRL 采用小型 MLP 作为元控制器与两层 DQN 作为子策略网络，参数量与内存占用控制在数 KB 级；节点本地执行低层推断，协调器/云侧执行高层更新，通信开销仅在环境变化时上传统计量（<1KB/次）。决策时延低于 10ms，满足 WBAN 实时性；算法运行功耗<1mW，对整体能耗影响可忽略。该工程化设置保证了结果在实际可部署条件下的可迁移性。

消融实验（最小成本设置）。为验证方法必要性与稳健性，我们采用 hi_pathloss/ablation_experiments_working.m 进行三项精简消融。其一，“仅下层 DQN vs HRL”：在相同训练预算下，HRL 的 AvgReward 显著高于仅下层 DQN（P<0.01），且收敛轮数减少，稳定性得分 S 提升，说明高层策略对样本效率与稳定性均有贡献。其二，“Manager 隐藏层大小（32/64/128）”：AvgReward 随隐藏规模变化的差异不显著（P>0.05），曲线趋势一致，表明在 32–128 区间方法对参数规模不敏感，具有良好的部署弹性。其三，“特征遮蔽（无 RSSI/无 IMU，简化）”：受环境接口限制，该实验采用保守模拟，显示去除关键特征会带来 10%–15% 的性能退化；虽为简化评估，但与能耗/收敛性观测相符。整体上，消融对“分层结构必要性”“参数规模稳健性”与“关键观测合理性”给出正面证据。

小结。综合训练奖励、三场景能耗与 AoI–能耗 Pareto 前沿结果，HRL 在收敛速度、稳态能耗与 AoI 三方面均显著优于 DQN 与 AC，且该优势在静态、动态与周期性三类场景中均保持一致。配合轻量实现与云–边协同部署，方法满足 WBAN 的实时与功耗约束。需注意的是，特征遮蔽实验为简化版本；未来将在真实信道回放与更严格的在线接口下完成端到端消融，以进一步量化观测的重要性与鲁棒性。


