审稿人意见：对"系统模型与问题建模"部分的学术质疑与改进建议

作为该领域的审稿专家，我对论文第二部分提出以下学术性质疑和改进建议：

**质疑1：信道模型的验证与适用性问题**

作者采用了基于实测数据的统计信道模型，但缺乏对模型验证方法和适用范围的详细说明。具体质疑包括：
- 路径损耗模型PL(d,f,θ) = PL₀ + 10n log₁₀(d/d₀) + Xσ + ΔPL(θ)中的参数是如何通过实测数据确定的？
- 该模型是否考虑了不同频段（如2.4GHz ISM频段）的传播特性差异？
- 人体姿态马尔可夫链模型的状态数量和转移概率是基于何种实验数据确定的？
- 模型是否考虑了不同体型、年龄、性别人群的个体差异？

**改进建议1：** 增加信道模型验证部分，包括模型参数的统计分析、不同场景下的模型精度评估，以及与现有IEEE 802.15.6标准信道模型的对比分析。同时，明确说明模型的适用条件和局限性。

**质疑2：能耗模型的完整性和准确性**

当前的能耗模型主要关注射频收发功耗，但对其他重要功耗组件的建模不够充分：
- 数字信号处理功耗（如调制解调、编解码）如何量化？
- 传感器采集功耗在不同采样率下的变化规律如何？
- 功率放大器效率η是否为常数？实际中η通常与输出功率相关。
- 电池老化对容量和内阻的影响是否考虑？

**改进建议2：** 建立更加完整的能耗模型，包括各功耗组件的详细建模和参数化。特别是功率放大器的非线性效率模型和电池动态特性模型。提供具体的功耗测量数据来验证模型的准确性。

**质疑3：问题形式化的数学严谨性**

优化问题的形式化存在一些数学表述不够严谨的地方：
- 约束条件中的"∀i,t"表述不够精确，应明确时间和节点的索引范围。
- 目标函数中的P_total,i(t)与前文定义的P_total存在符号不一致。
- 缺乏对优化问题复杂度的理论分析，如NP-hard性质的证明。
- QoS约束的数学表述缺失，仅在文字中提及。

**改进建议3：** 规范数学符号的使用，确保前后一致性。增加优化问题复杂度的理论分析，并将QoS约束正式纳入数学模型中。考虑引入拉格朗日乘数法或其他数学工具来分析问题的可解性。

**质疑4：系统模型的实际可实现性**

论文假设节点可以实时获得准确的信道状态信息和剩余能量信息，但这在实际系统中可能面临挑战：
- RSSI测量的精度和实时性如何保证？
- 剩余能量的估计方法是什么？库仑计数法的累积误差如何处理？
- 中央协调器如何及时获得所有节点的状态信息？通信开销如何？
- 功率控制决策的执行延迟对系统性能的影响如何？

**改进建议4：** 增加系统实现方面的讨论，包括状态信息获取的方法、精度分析和通信开销评估。考虑在系统模型中引入测量噪声和信息延迟等实际因素。

**质疑5：与现有标准和技术的兼容性**

论文没有充分讨论所提方法与现有WBAN标准（如IEEE 802.15.6）的兼容性：
- 所提的功率控制方法如何与标准的MAC协议集成？
- 是否需要修改现有的协议栈？
- 与其他功率管理技术（如动态电压频率调节）的协同如何实现？
- 在多用户环境下的干扰管理如何处理？

**改进建议5：** 增加与现有标准兼容性的分析，讨论实际部署时的协议修改需求。考虑多用户场景下的干扰建模和管理策略。

**总体评价：**
该部分内容在系统建模方面具有一定的创新性，特别是将人体姿态建模为马尔可夫链的思路值得肯定。但在模型验证、数学严谨性和实际可实现性方面还需要进一步完善。建议作者在修改时重点关注模型的验证方法、数学表述的规范性以及与实际系统的结合度。
