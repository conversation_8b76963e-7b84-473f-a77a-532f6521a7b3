% 测试修复后的demo_aoi_expected_results功能
% 简化版本用于验证修复效果

close all;
clear;
clc;

fprintf('=== 测试修复后的AoI演示功能 ===\n');

try
    % 生成测试数据
    fprintf('生成测试数据...\n');
    
    algorithms = {'分层RL', 'DQN', '演员-评论家', '固定功率'};
    scenarios = {'静态监测', '动态转换', '周期性运动'};
    
    aoi_data = [
        [10.2, 18.5, 14.8];
        [16.8, 28.3, 22.1];
        [13.5, 23.2, 18.6];
        [22.4, 35.7, 28.9];
    ];
    
    energy_data = [
        [1.85, 3.42, 2.68];
        [3.15, 4.89, 3.87];
        [2.34, 3.98, 3.12];
        [4.25, 5.67, 4.98];
    ];
    
    fprintf('✓ 数据生成完成\n');
    
    % 测试Pareto图生成（修复后的版本）
    fprintf('测试Pareto图生成...\n');
    
    figure('Position', [100, 100, 1200, 400]);
    
    colors = [0.2, 0.6, 0.8; 0.8, 0.4, 0.2; 0.4, 0.8, 0.4; 0.6, 0.6, 0.6];
    markers = {'o', 's', '^', 'x'};
    marker_sizes = [100, 100, 100, 120];
    
    for s = 1:length(scenarios)
        subplot(1, 3, s);
        hold on;
        
        % 绘制各算法的点
        for a = 1:length(algorithms)
            scatter(energy_data(a, s), aoi_data(a, s), ...
                   marker_sizes(a), colors(a, :), 'Marker', markers{a}, ...
                   'LineWidth', 2, 'DisplayName', algorithms{a});
        end
        
        % 测试修复后的Pareto前沿线绘制
        pareto_energy = [energy_data(1, s), energy_data(3, s)];
        pareto_aoi = [aoi_data(1, s), aoi_data(3, s)];
        
        % 使用修复后的方法
        h_line = plot(pareto_energy, pareto_aoi, 'k--', 'LineWidth', 1.5);
        h_line.Color = [0, 0, 0, 0.7]; % RGBA格式设置透明度
        
        xlabel('平均能耗 (mJ)', 'FontSize', 12);
        ylabel('平均AoI (ms)', 'FontSize', 12);
        title(sprintf('%s场景', scenarios{s}), 'FontSize', 14, 'FontWeight', 'bold');
        
        xlim([1, 6]);
        ylim([8, 40]);
        
        legend('Location', 'northeast', 'FontSize', 10);
        grid on;
        grid minor;
    end
    
    % 测试sgtitle兼容性
    try
        sgtitle('AoI-能耗 Pareto前沿图测试', 'FontSize', 16, 'FontWeight', 'bold');
        fprintf('✓ sgtitle函数正常工作\n');
    catch
        % 如果sgtitle不支持，使用替代方案
        fprintf('⚠ sgtitle不支持，使用替代标题\n');
        % 可以在这里添加替代的标题方案
    end
    
    % 保存图片
    saveas(gcf, 'test_pareto_fix.png');
    saveas(gcf, 'test_pareto_fix.fig');
    
    fprintf('✓ Pareto图生成完成\n');
    
    % 测试场景对比图
    fprintf('测试场景对比图生成...\n');
    
    figure('Position', [200, 200, 1000, 800]);
    
    % AoI对比图
    subplot(2, 1, 1);
    bar_data = aoi_data';
    bar_handle = bar(bar_data, 'grouped');
    
    % 设置颜色
    for i = 1:length(bar_handle)
        bar_handle(i).FaceColor = colors(i, :);
        bar_handle(i).EdgeColor = colors(i, :) * 0.8;
        bar_handle(i).LineWidth = 1;
    end
    
    xlabel('场景', 'FontSize', 12);
    ylabel('平均AoI (ms)', 'FontSize', 12);
    title('三种场景下的平均信息年龄对比', 'FontSize', 14, 'FontWeight', 'bold');
    legend(algorithms, 'Location', 'northwest', 'FontSize', 11);
    set(gca, 'XTickLabel', scenarios);
    grid on;
    ylim([0, 40]);
    
    % 能耗对比图
    subplot(2, 1, 2);
    bar_data = energy_data';
    bar_handle = bar(bar_data, 'grouped');
    
    % 设置颜色
    for i = 1:length(bar_handle)
        bar_handle(i).FaceColor = colors(i, :);
        bar_handle(i).EdgeColor = colors(i, :) * 0.8;
        bar_handle(i).LineWidth = 1;
    end
    
    xlabel('场景', 'FontSize', 12);
    ylabel('平均能耗 (mJ)', 'FontSize', 12);
    title('三种场景下的平均能耗对比', 'FontSize', 14, 'FontWeight', 'bold');
    legend(algorithms, 'Location', 'northwest', 'FontSize', 11);
    set(gca, 'XTickLabel', scenarios);
    grid on;
    ylim([0, 6]);
    
    % 保存图片
    saveas(gcf, 'test_scenario_fix.png');
    saveas(gcf, 'test_scenario_fix.fig');
    
    fprintf('✓ 场景对比图生成完成\n');
    
    % 输出数值结果
    fprintf('\n=== 数值结果测试 ===\n');
    
    overall_aoi = mean(aoi_data, 2);
    overall_energy = mean(energy_data, 2);
    aoi_energy_product = overall_aoi .* overall_energy;
    
    fprintf('%-12s %-12s %-12s %-12s\n', '算法', '平均AoI(ms)', '平均能耗(mJ)', 'AoI×能耗');
    fprintf('%-12s %-12s %-12s %-12s\n', '----', '----------', '----------', '--------');
    
    [~, rank_idx] = sort(aoi_energy_product);
    
    for i = 1:length(rank_idx)
        idx = rank_idx(i);
        fprintf('%-12s %-12.1f %-12.2f %-12.1f\n', ...
               algorithms{idx}, overall_aoi(idx), overall_energy(idx), aoi_energy_product(idx));
    end
    
    fprintf('\n=== 所有测试通过！ ===\n');
    fprintf('生成的文件:\n');
    fprintf('✓ test_pareto_fix.png - 修复后的Pareto图\n');
    fprintf('✓ test_scenario_fix.png - 场景对比图\n');
    
    fprintf('\n现在可以安全运行:\n');
    fprintf('demo_aoi_expected_results\n');
    
catch ME
    fprintf('❌ 测试失败:\n');
    fprintf('错误信息: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
    
    fprintf('\n可能的解决方案:\n');
    fprintf('1. 检查MATLAB版本是否支持所有使用的函数\n');
    fprintf('2. 确保当前目录有写入权限\n');
    fprintf('3. 检查是否有足够的内存和磁盘空间\n');
end
