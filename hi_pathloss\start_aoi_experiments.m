% AoI实验启动脚本
% 简单的启动脚本，避免函数名冲突

close all;
clear;
clc;

fprintf('========================================\n');
fprintf('AoI对比实验启动脚本\n');
fprintf('========================================\n\n');

try
    % 步骤1: 代码验证
    fprintf('步骤1: 代码验证...\n');
    verify_aoi_code();
    fprintf('✓ 代码验证完成\n\n');
    
    % 步骤2: 预期结果演示
    fprintf('步骤2: 预期结果演示...\n');
    demo_aoi_expected_results();
    fprintf('✓ 预期结果演示完成\n\n');
    
    % 步骤3: 快速测试
    fprintf('步骤3: 快速测试...\n');
    test_aoi_experiment();
    fprintf('✓ 快速测试完成\n\n');
    
    % 询问是否运行完整实验
    user_choice = input('是否运行完整实验？(y/n): ', 's');
    
    if strcmpi(user_choice, 'y') || strcmpi(user_choice, 'yes')
        fprintf('步骤4: 完整实验...\n');
        aoi_comparison_experiment();
        fprintf('✓ 完整实验完成\n\n');
    else
        fprintf('跳过完整实验\n\n');
    end
    
    % 显示结果文件
    fprintf('========================================\n');
    fprintf('实验完成！生成的文件:\n');
    fprintf('========================================\n');
    
    % 列出生成的文件
    files_to_check = {
        'expected_aoi_pareto_comparison.png', '预期Pareto前沿图';
        'expected_aoi_scenario_comparison.png', '预期场景对比图';
        'test_aoi_comparison.png', '快速测试结果图';
        'test_aoi_results.mat', '快速测试数据';
        'aoi_pareto_comparison.png', '完整实验Pareto图';
        'aoi_scenario_comparison.png', '完整实验场景图';
        'aoi_comparison_results.mat', '完整实验数据';
        'aoi_analysis_report.txt', '详细分析报告'
    };
    
    for i = 1:size(files_to_check, 1)
        filename = files_to_check{i, 1};
        description = files_to_check{i, 2};
        
        if exist(filename, 'file')
            fprintf('✓ %s - %s\n', filename, description);
        else
            fprintf('✗ %s - %s (未生成)\n', filename, description);
        end
    end
    
    fprintf('\n========================================\n');
    fprintf('使用说明:\n');
    fprintf('1. 查看PNG图片文件了解实验结果\n');
    fprintf('2. 阅读README_AOI_EXPERIMENT.md获取详细说明\n');
    fprintf('3. 使用MAT文件进行进一步分析\n');
    fprintf('========================================\n');
    
catch ME
    fprintf('❌ 实验运行出错:\n');
    fprintf('错误信息: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
    
    fprintf('\n故障排除建议:\n');
    fprintf('1. 确保当前目录为hi_pathloss文件夹\n');
    fprintf('2. 检查MATLAB版本兼容性\n');
    fprintf('3. 确保有足够的内存和磁盘空间\n');
    fprintf('4. 查看README_AOI_EXPERIMENT.md中的故障排除部分\n');
end
