3.3 双层RL架构设计

本节详细阐述了针对WBAN功率控制问题设计的双层强化学习架构。该架构充分考虑了WBAN系统的物理约束和计算资源限制，通过高层的环境适应策略和低层的精细功率控制实现了多时间尺度的协同优化。

3.3.1 架构设计原则与理论基础

双层RL架构的设计遵循三个核心原则：时间尺度分离原则、状态抽象原则和计算复杂度约束原则。时间尺度分离原则基于Sutton等人(1999)的Options理论和Bacon等人(2017)的Option-Critic框架建立。根据分层强化学习理论，当高层和低层的时间尺度分离比大于10时，分层学习能够有效收敛而不显著影响性能。在WBAN环境中，我们通过对8个节点在不同人体活动场景下的500小时实测数据分析发现，信道统计特性的变化时间常数为2-10秒，而瞬时功率调整的响应时间为10-50毫秒，时间尺度分离比约为40-1000，满足分层学习的理论要求。实验验证表明，在分离比为100的设置下，分层方法相比单层方法的性能损失小于3%，证实了时间尺度分离的有效性。

状态抽象原则旨在将高维的原始状态空间映射到低维的抽象状态空间，从而降低学习复杂度。我们采用基于价值函数梯度的重要性分析方法，通过计算每个状态变量对Q值函数的梯度范数来评估其重要性。具体而言，对于状态变量s_i，计算||∇_{s_i}Q(s,a)||在所有状态-动作对上的平均值，选择梯度范数最大的前k个变量作为高层状态。通过对1000个episode的数据分析，我们发现信道时间相关性、RSSI方差和网络平均能量的梯度范数分别为0.73、0.68和0.61，远高于其他变量，因此选择这三个变量构成高层状态空间。为验证抽象的有效性，我们采用价值函数近似误差分析，结果表明抽象导致的性能损失仅为4.2%，在可接受范围内。

计算复杂度约束原则基于WBAN节点的实际硬件限制制定。目标平台为ARM Cortex-M4处理器，具有48MHz主频、64KB RAM和32KB Flash存储空间。在这种资源受限的环境下，算法的参数数量、计算复杂度和内存占用都必须严格控制。我们将总参数预算设定为不超过4000个，单次决策的推理时间控制在毫秒级别，算法运行功耗不超过系统总功耗的5%。

3.3.2 高层策略：环境适应与长期规划
高层策略承担着环境变化感知以及长期规划决策的职责，其关键任务在于依据网络的整体状况挑选适宜的控制模式。基于前述的重要性分析结果，高层状态空间设计为三维向量：信道时间相关性{\ R}_{\mathrm{corr}}\left(t\right)、RSSI方差\ \sigma_{\mathrm{RSSI}}^2(t)和网络平均剩余能量{\ E}_{\mathrm{network}}\left(t\right)。为了验证此种设计有有效性，开展了对比实验：将采用完整五维状态空间的策略当作基准，三维状态空间的策略性能损失仅仅是4.2%，然而二维状态空间的性能损失却达到了11.8%。这意味着三维状态表示在维持性能的情况下有效地降低了计算复杂度，节省了计算能耗。
我们借助相关性分析对所选变量的独立性给予了验证，三个状态变量之间的相关系数都小于0.5，这意味着它们提供了相对独立的信息，有效减少了信息冗余的情况。同时这三个变量与网络性能指标，像生存时间、包成功率，其相关系数分别是0.67、0.72和0.81，这证实了它们对于决策有着意义。
高层动作空间是依据WBAN系统的实际需求来设计的，除了保持、重训练、调参这三种动作之外，还覆盖了三种Options内部策略，分别是节能优先、性能优先以及自适应平衡。节能优先借助降低探索噪声（\sigma\ = 0.05）以及增加目标平滑参数的方式，来削减功率波动，适用于网络平均剩余能量低于30%的场景，其初始条件为{\ E}_{\mathrm{network}}\left(t\right) < 0.3，终止条件为{\ E}_{\mathrm{network}}\left(t\right) > 0.5或连续运行超过100个时间步。性能优先凭借增加探索噪声（\sigma\ = 0.2）以及减少延迟更新间隔的手段，可快速适应环境变化，适用于信道质量不稳定但能量充足的场景，其初始条件为\sigma_{\mathrm{RSSI}}^2 > 0.6且{\ E}_{\mathrm{network}}\left(t\right) > 0.4，终止条件为{\ \sigma}_{\mathrm{RSSI}}^2 < 0.4或{\ E}_{\mathrm{network}}\left(t\right) < 0.3。自适应平衡被当作默认选择，采用的是标准参数配置（\sigma\ = 0.1），适用于其他所有的场景，它的终止条件是基于性能监控指标出现了十分突出的变化。
为达成硬件约束条件，高层网络采用轻量化设计方案，其网络结构设计为包含16个神经元的隐藏层，选用Tanh激活函数来防止梯度消失状况出现，输出层运用Softmax激活函数，以此保证输出概率的归一化效果，整个网络的参数数量仅有80个，远远少于预算限制，为低层网络留出了充裕的参数空间。
高层学习算法采用Option-Critic方法，此方法可同时对Option选择策略、内部策略以及终止函数展开学习。Option选择策略所起的作用是，明确在当前网络状态时应当选取何种动作模式，内部策略则是对特定模式下的具体行为给予定义，而终止函数负责判定何时终止当前模式并切换至其他模式。借助对这三个组件进行联合优化，系统可自行学习到合适的分层策略。

3.3.3 低层策略：精细功率控制与实时优化

低层策略专注于毫秒级的实时功率控制，其主要任务是根据当前的网络状态和高层指令，为每个传感器节点选择最优的发射功率。为验证连续建模的必要性，我们对比了连续功率控制与离散功率控制的性能。实验结果表明，连续建模在网络生存时间上比离散建模提升了7.3%，在包成功率上提升了5.1%，验证了连续建模的优势。

在算法选择方面，我们在WBAN仿真环境中对比了DDPG、TD3和SAC三种算法的性能。TD3在收敛稳定性方面优于DDPG（训练方差降低32%），在计算效率方面优于SAC（推理时间减少24%），同时在最终性能上与两者相当。具体而言，TD3通过双Critic网络有效缓解了过估计偏差，通过延迟策略更新提高了训练稳定性，通过目标策略平滑增强了策略鲁棒性，这些特性特别适合WBAN的动态环境。

低层状态空间的设计充分考虑了WBAN系统的特点。状态向量包含每个节点的RSSI值和剩余能量水平，以及反映网络整体负载的全局状态。为了提高数值稳定性，所有状态变量都通过线性变换映射到标准化区间。这种设计既保证了状态表示的完整性，又控制了状态空间的维度，使得网络能够高效地进行学习和推理。

低层动作空间定义为标准化的连续动作空间，每个节点的动作值在-1到1之间。通过线性映射将标准化动作转换为实际的发射功率值，功率范围覆盖了从-25dBm到0dBm的典型WBAN工作区间。这种设计的优势在于能够充分利用连续控制的精度优势，同时通过tanh激活函数自然满足功率约束条件。

低层网络架构采用Actor-Critic结构，其中Actor网络负责策略学习，Critic网络负责价值评估。为了满足硬件约束，网络结构经过精心优化。Actor网络包含两个隐藏层，分别有32个和16个神经元，使用ReLU激活函数以提高计算效率。Critic网络采用双网络结构以提高训练稳定性，每个网络包含一个32神经元的隐藏层。整个低层网络的参数数量约为3200个，在预算范围内为系统提供了足够的表达能力。

TD3学习算法的关键参数通过系统性的超参数优化确定。学习率通过网格搜索在[0.0001, 0.01]范围内优化，最终选择0.001以平衡收敛速度和稳定性。延迟更新间隔通过对比d=1,2,3的性能确定为2，既保证了价值函数的充分训练又避免了过度延迟。探索噪声通过探索-利用平衡分析确定为0.1，目标网络软更新系数设为0.005以保证目标网络的稳定更新。超参数敏感性分析表明，学习率和探索噪声对性能影响最大，其他参数在合理范围内变化时性能相对稳定。

3.3.4 层间通信与协调机制

双层架构的有效运行依赖于高层和低层之间的高效通信和协调。高层向低层的通信主要通过Option切换信号实现，当高层决定切换操作模式时，会向低层发送相应的参数调整指令。在保守模式下，系统会降低探索噪声并增加目标平滑参数，以减少不必要的功率波动。在平衡模式下，系统使用默认参数配置，保持稳定的学习行为。在激进模式下，系统会增加探索噪声并减少延迟更新间隔，以快速适应环境变化。

低层向高层的通信主要通过性能监控指标实现。系统定期计算平均奖励、策略熵和TD误差方差等关键指标，并将这些信息反馈给高层。平均奖励反映了当前策略的整体性能，策略熵反映了策略的探索程度，TD误差方差反映了价值函数的学习质量。当这些指标出现异常波动时，系统会立即向高层报告，触发相应的调整机制。

为了保证系统的鲁棒性，我们设计了完善的容错机制。当Option切换发生时，系统会保留经验回放缓冲区中的历史数据，避免学习过程的中断。同时，系统会监控层间通信的延迟和丢包情况，当通信质量下降时会自动启用备用通信机制。这些设计确保了双层架构在复杂的WBAN环境中能够稳定可靠地运行。

为验证架构的实际可行性，我们在ARM Cortex-M4开发板上进行了性能测试。实测结果显示，高层网络单次推理耗时0.9ms，低层网络单次推理耗时1.4ms，层间通信延迟0.2ms，总计算时间2.5ms，满足20ms控制周期的实时性要求。内存占用方面，网络参数占用13.2KB，经验回放缓冲区占用6KB，总内存需求19.2KB，在64KB RAM限制内。功耗测试表明，算法运行功耗约为1.2mW，占系统总功耗的3.8%，满足低功耗要求。

通过上述设计和验证，双层RL架构实现了高效的多时间尺度协同优化。高层策略基于严格的理论基础进行环境适应，低层策略通过充分的实验验证实现精细控制，两者通过优化的通信机制实现有机协调。整个架构在满足WBAN硬件约束的前提下，显著提高了功率控制的效率和适应性，为WBAN系统的实际部署提供了经过验证的可行解决方案。
