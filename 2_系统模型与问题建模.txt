2 系统模型与问题建模

2.1 WBAN网络拓扑与通信流程

无线体域网(WBAN)是一种专门针对人体环境设计的短距离无线通信网络，通常由分布在人体表面或植入体内的多个传感器节点构成。本研究考虑的WBAN网络拓扑采用星型拓扑结构，其中包含一个中心协调器节点(通常位于腰部或胸部)和若干个传感器节点分布在人体的不同部位，如手腕、胸部、背部等关键监测位置。传感器节点负责采集生理信号数据，包括心电图(ECG)、肌电图(EMG)、加速度等生物医学参数，并通过无线链路将数据传输至协调器节点。协调器节点作为数据汇聚点，不仅接收来自各传感器节点的数据，还负责网络的整体协调和管理，包括信道接入控制、功率管理和数据转发等功能。

在通信流程方面，WBAN采用基于时分多址(TDMA)的MAC协议来避免节点间的相互干扰。每个传感器节点在分配的时隙内进行数据传输，传输过程遵循IEEE 802.15.6标准规范。由于人体姿态的动态变化会直接影响节点间的信道质量，通信流程需要具备良好的适应性。当人体处于不同姿态(如静立、行走、弯腰等)时，节点间的相对位置和信道传播特性会发生显著变化，这要求功率控制机制能够实时感知这些变化并做出相应调整。

2.2 信道与传播模型

WBAN的信道环境具有高度的时变特性和复杂性，主要体现在路径损耗的非线性变化和多径衰落效应。本研究基于实测数据建立了针对人体信道的路径损耗模型。路径损耗可以表示为PL(d) = PL₀ + 10n log₁₀(d/d₀) + Xσ，其中PL₀为参考距离d₀处的路径损耗，n为路径损耗指数，d为节点间距离，Xσ为零均值高斯阴影衰落项。

在人体环境中，路径损耗指数n不是固定常数，而是随着人体姿态和节点位置的变化而动态变化。研究表明，当人体处于不同姿态时，n值可能在2.0到6.0之间变化。例如，当人体直立时，胸部到手腕的链路路径损耗指数约为3.2，而当人体弯腰时，该指数可能增加到4.8，这种变化直接影响了链路的可靠性和所需的发射功率。此外，人体组织的介电常数和电导率在不同频段下表现出频率选择性特征，进一步增加了信道建模的复杂性。

为了准确刻画这种时变特性，本研究采用了基于运动捕获数据的信道建模方法。通过分析不同姿态序列下的RSSI变化模式，建立了姿态-路径损耗的映射关系。这种建模方法能够更真实地反映实际应用场景中的信道变化特征，为功率控制算法的设计提供了可靠的理论基础。

2.3 传感器节点能耗模型

传感器节点的能耗主要包括三个方面：数据处理能耗、射频收发能耗和电路能耗。其中，射频收发能耗是功率控制的主要优化目标，通常占据节点总能耗的60%-80%。节点的发射功率能耗可以建模为Ptx = Pamp + Pelec，其中Pamp为功率放大器消耗的功率，与发射功率成正比关系，Pelec为射频电路的固定功耗。

具体而言，功率放大器的能耗可以表示为Pamp = ε·Ptx²，其中ε为功放效率参数，通常在0.1-0.3之间。射频电路能耗Pelec包括频率合成器、混频器、滤波器等电路模块的功耗，典型值约为10-50mW。接收端的能耗主要由低噪声放大器、解调器等电路组成，通常为固定值Prx。

除了射频收发能耗外，节点还存在三种基本工作状态：发送状态、接收/监听状态和睡眠状态。发送状态的瞬时功耗最高，但持续时间较短；接收/监听状态的功耗相对较低但持续时间较长；睡眠状态的功耗最低，通常仅为微瓦级别。合理的功率控制策略不仅要优化发射功率水平，还要兼顾节点在不同状态间的切换时机，以实现整体能耗的最小化。

基于电池容量限制，节点的剩余能量状态也是功率控制决策的重要因素。当节点剩余能量较低时，应采用更加保守的功率控制策略，优先保证关键数据的传输可靠性，而非追求传输速率的最大化。

2.4 问题描述：在保证链路可靠性的前提下最小化整体能耗

本研究的核心问题可以表述为：在满足WBAN通信可靠性要求的前提下，通过优化各节点的发射功率，实现网络整体能耗的最小化，从而延长网络生存时间。这是一个多目标约束优化问题，需要在能耗、可靠性和服务质量之间寻求最优平衡。

形式化地，该优化问题可以表述为：

minimize Σᵢ₌₁ᴺ Eᵢ(Pᵢ,tₓ)

subject to:
- PRᵢ ≥ PRᵢ,min, ∀i ∈ N (包接收率约束)
- RSNRᵢ ≥ RSNRᵢ,th, ∀i ∈ N (信噪比门限约束)
- Pᵢ,min ≤ Pᵢ,tₓ ≤ Pᵢ,max, ∀i ∈ N (功率范围约束)
- Dᵢ ≤ Dᵢ,max, ∀i ∈ N (时延约束)

其中，N为网络中传感器节点总数，Eᵢ(Pᵢ,tₓ)表示节点i在发射功率Pᵢ,tₓ下的总能耗，PRᵢ为节点i的包接收率，RSNRᵢ为接收信噪比，Dᵢ为传输时延。这些约束条件确保了网络在功耗优化过程中仍能维持基本的通信服务质量。

该问题的主要挑战在于：首先，人体信道的时变特性使得传统的静态功率分配方法无法适应动态环境；其次，多个节点之间存在相互干扰，需要考虑节点间的耦合关系；最后，问题的解空间随着节点数量和功率级别数量呈指数增长，传统的穷举搜索方法在计算上不可行。

因此，本研究提出采用分层强化学习方法来解决这一复杂的动态优化问题。通过构建高层的环境感知模块和低层的功率决策模块，实现对时变信道环境的自适应响应，在保证通信可靠性的前提下最大程度地降低网络整体能耗。 