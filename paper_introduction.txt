1 引言

1.1 无线体域网 (WBAN) 概述与应用场景

无线体域网（Wireless Body Area Network, WBAN）是一种专门为人体周围或体内传感器节点设计的短距离无线通信网络，通常覆盖范围在2-3米内。WBAN通过在人体表面、衣物或植入体内的微型传感器节点，实现生理参数的实时监测与数据传输，为个性化医疗、健康管理、运动监测等应用提供了重要的技术支撑[1-3]。

典型的WBAN应用场景包括：（1）医疗监护：心电图（ECG）、肌电图（EMG）、血氧饱和度等生理信号的连续监测；（2）慢性病管理：糖尿病患者血糖水平监控、高血压患者血压跟踪等；（3）运动健康：步态分析、运动强度评估、康复训练监测；（4）应急医疗：突发疾病预警、老年人跌倒检测等。随着物联网技术的快速发展，WBAN在智慧医疗体系中的地位日益重要，市场规模预计将在2025年达到数十亿美元[4]。

1.2 节点功率控制挑战与研究意义

WBAN功率控制面临着多重技术挑战，这些挑战的复杂性和相互耦合性使得传统单一策略难以有效解决：

（1）能量约束极其严格：WBAN节点通常采用容量仅为几十到几百mAh的小型电池，在连续监测模式下需要工作数月甚至数年。据统计，发射功率消耗占节点总能耗的60-80%，是影响网络寿命的关键因素[5]。

（2）人体信道复杂多变：人体组织的高介电常数（εr=50-80）和电导率（σ=0.5-2 S/m）导致信号衰减严重，路径损耗可达40-60dB。更重要的是，人体姿态变化引起的信道波动具有多时间尺度特性：快速变化（秒级）对应肢体运动，慢速变化（分钟级）对应姿态转换，这种多尺度特性需要不同的控制策略[6,7]。

（3）实时性与可靠性矛盾：医疗监测应用要求数据传输的实时性（延迟<100ms）和可靠性（误码率<10^-6），但频繁的功率调整会增加协议开销，形成性能矛盾[8]。

（4）分布式决策复杂性：WBAN通常采用星型拓扑，多个传感器节点需要在缺乏全局信息的情况下独立做出功率控制决策，同时避免相互干扰，这构成了一个复杂的多智能体优化问题[9]。

这些挑战的根本原因在于WBAN环境的多层次、多尺度特性，需要能够处理不同抽象层次决策的智能控制方法。因此，设计分层的智能功率控制算法，实现跨时间尺度的协调优化，对于WBAN的实用化具有重要意义。

1.3 现有功率控制方法评述

现有的WBAN功率控制方法可以分为传统方法和智能方法两大类：

**传统功率控制方法：**

（1）基于门限的功率控制：根据RSSI或SNR门限调整发射功率。文献[10]提出了自适应门限调整机制，但在快速信道变化下仍存在功率震荡问题，能耗节省有限（10-15%）。

（2）博弈论方法：Meshkati等[12]将WBAN功率控制建模为非合作博弈，通过纳什均衡求解。虽然理论性能较好，但需要O(N³)的计算复杂度和全局信息交换，不适合实时应用。

（3）凸优化方法：文献[14,15]基于拉格朗日乘数法求解功率控制的最优解，能够保证全局最优性，但假设信道模型已知且准静态，实际应用受限。

**智能功率控制方法：**

（4）模糊逻辑控制：Latre等[16]设计了基于模糊推理的功率控制器，能够处理信道的不确定性，但规则设计依赖专家经验，缺乏自适应能力。

（5）神经网络方法：文献[17]采用BP神经网络预测信道状态并调整功率，但需要大量训练数据，且在新环境下泛化能力有限。

（6）基础强化学习：Pham等[18]首次将Q-learning应用于WBAN功率控制，在静态场景下取得了20%的能耗节省。但该方法状态空间设计简单，未考虑人体姿态的多尺度变化特性。

**现有方法的局限性分析：**

通过对比分析发现，现有方法存在以下共同局限性：（1）缺乏对人体信道多时间尺度特性的建模；（2）无法处理场景切换时的策略适应问题；（3）单一决策层次难以平衡实时性与优化性能；（4）缺乏对长期网络寿命的考虑。这些局限性表明，需要设计能够处理多层次、多尺度决策的智能控制方法。

1.4 强化学习在无线通信中的进展

近年来，强化学习（Reinforcement Learning, RL）作为一种无需先验模型的智能决策方法，在无线通信领域展现出巨大潜力。RL通过与环境的交互学习最优策略，能够适应动态变化的信道条件，为复杂的资源分配问题提供了新的解决思路[18-20]。

在功率控制方面，深度Q网络（Deep Q-Network, DQN）因其出色的非线性逼近能力和样本效率，成为研究热点。文献[21]首次将DQN应用于蜂窝网络的功率控制，实现了干扰管理和能耗优化的平衡。文献[22]针对认知无线电网络，设计了基于DQN的动态功率分配算法，显著提升了频谱利用率。在WBAN领域，文献[23]探索了Q-learning在简单功率控制中的应用，但仅考虑了静态场景，未涉及复杂的人体姿态变化。

然而，传统的单层强化学习方法在面对WBAN多尺度、多层次的决策问题时存在根本性不足：

**理论局限性：**（1）维度灾难：WBAN中每个节点的状态空间包含信道状态、能量水平、队列长度等多个维度，导致状态空间呈指数级增长（O(|S|^N)），学习效率急剧下降[24]；（2）时间尺度不匹配：人体姿态变化的慢时间尺度（分钟级）与功率调整的快时间尺度（秒级）存在两个数量级的差异，单一智能体难以同时处理；（3）稀疏奖励问题：网络寿命等长期目标的奖励信号极其稀疏，传统RL方法难以有效学习。

**实证证据：**文献[25]的实验表明，在包含5个节点的WBAN中，单层DQN的收敛时间超过10^6步，且在动态场景下性能显著下降（能耗节省从静态场景的18%降至6%）。

**分层强化学习的优势：**分层强化学习（HRL）通过将复杂任务分解为多个子任务，能够有效解决上述问题：（1）状态抽象降低了单个策略的复杂度；（2）时间抽象实现了多尺度决策的协调；（3）目标分解提供了密集的中间奖励信号[26,27]。这为WBAN功率控制提供了理论上可行的解决方案。

1.5 本文贡献与组织结构

针对WBAN功率控制的多尺度、多层次挑战，本文提出了一种基于分层强化学习的智能功率控制方法。**主要技术贡献包括：**

（1）**多尺度信道建模**：基于真实人体路径损耗数据（13_01、13_04、35_01），构建了考虑快速肢体运动和慢速姿态转换的双时间尺度信道模型，为分层决策提供了理论基础。

（2）**分层决策架构**：设计了Options-DQN框架，高层策略（Meta-Controller）基于场景特征选择合适的子策略（Options），低层策略（Option-Controller）在选定时间窗口内执行具体的功率调整，实现了O(log|S|)的状态空间压缩。

（3）**自适应奖励塑形**：提出了基于网络寿命预测的动态奖励函数，将长期目标分解为短期子目标，解决了传统RL中的稀疏奖励问题，收敛速度提升了3-5倍。

（4）**跨场景泛化机制**：设计了基于元学习的策略迁移方法，使训练好的策略能够快速适应新的人体姿态模式，适应时间从传统方法的数小时降至数分钟。

**研究范围与假设条件：**本文研究基于以下假设：（1）星型网络拓扑，单个协调器节点；（2）准静态人体姿态变化（变化周期>10s）；（3）离散功率级别控制（4-8个级别）；（4）理想MAC层协议，无冲突检测延迟。

**实验验证策略：**选择能耗、包成功率和网络寿命作为主要评估指标的原因：（1）能耗直接关系到电池寿命，是WBAN最关键的性能指标；（2）包成功率反映了通信可靠性，是医疗监测的基本要求；（3）网络寿命综合体现了能耗优化的长期效果，是实际部署的核心关切。这三个指标构成了WBAN性能评估的完整体系。

本文其余部分组织如下：第2节建立系统模型并定义问题；第3节详细阐述分层强化学习功率控制方法；第4节介绍实验设计；第5节分析实验结果；第6节讨论方法的局限性和扩展性；第7节总结全文并展望未来工作。 