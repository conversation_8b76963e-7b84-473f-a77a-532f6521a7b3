% 快速测试demo功能的最小化版本

close all;
clear;
clc;

fprintf('=== 快速Demo测试 ===\n');

try
    % 基本数据
    algorithms = {'分层RL', 'DQN', '演员-评论家', '固定功率'};
    scenarios = {'静态监测', '动态转换', '周期性运动'};
    
    aoi_data = [
        [10.2, 18.5, 14.8];
        [16.8, 28.3, 22.1];
        [13.5, 23.2, 18.6];
        [22.4, 35.7, 28.9];
    ];
    
    energy_data = [
        [1.85, 3.42, 2.68];
        [3.15, 4.89, 3.87];
        [2.34, 3.98, 3.12];
        [4.25, 5.67, 4.98];
    ];
    
    fprintf('数据准备完成\n');
    
    % 测试基本绘图
    figure('Position', [100, 100, 800, 600]);
    
    % 简单的散点图测试
    subplot(2, 2, 1);
    scatter(energy_data(1, :), aoi_data(1, :), 100, 'b', 'o', 'filled');
    xlabel('能耗 (mJ)');
    ylabel('AoI (ms)');
    title('分层RL');
    grid on;
    
    subplot(2, 2, 2);
    scatter(energy_data(2, :), aoi_data(2, :), 100, 'r', 's', 'filled');
    xlabel('能耗 (mJ)');
    ylabel('AoI (ms)');
    title('DQN');
    grid on;
    
    subplot(2, 2, 3);
    scatter(energy_data(3, :), aoi_data(3, :), 100, 'g', '^', 'filled');
    xlabel('能耗 (mJ)');
    ylabel('AoI (ms)');
    title('演员-评论家');
    grid on;
    
    subplot(2, 2, 4);
    scatter(energy_data(4, :), aoi_data(4, :), 100, [0.6, 0.6, 0.6], 'x');
    xlabel('能耗 (mJ)');
    ylabel('AoI (ms)');
    title('固定功率');
    grid on;
    
    % 保存测试图
    saveas(gcf, 'quick_test_scatter.png');
    fprintf('✓ 散点图测试通过\n');
    
    % 测试柱状图
    figure('Position', [200, 200, 800, 600]);
    
    subplot(2, 1, 1);
    bar(aoi_data');
    xlabel('场景');
    ylabel('AoI (ms)');
    title('AoI对比');
    legend(algorithms, 'Location', 'best');
    set(gca, 'XTickLabel', scenarios);
    
    subplot(2, 1, 2);
    bar(energy_data');
    xlabel('场景');
    ylabel('能耗 (mJ)');
    title('能耗对比');
    legend(algorithms, 'Location', 'best');
    set(gca, 'XTickLabel', scenarios);
    
    % 保存测试图
    saveas(gcf, 'quick_test_bar.png');
    fprintf('✓ 柱状图测试通过\n');
    
    % 测试简单的线图（不使用透明度）
    figure('Position', [300, 300, 600, 400]);
    
    x = 1:3;
    plot(x, aoi_data(1, :), 'b-o', 'LineWidth', 2, 'MarkerSize', 8, 'DisplayName', '分层RL');
    hold on;
    plot(x, aoi_data(2, :), 'r-s', 'LineWidth', 2, 'MarkerSize', 8, 'DisplayName', 'DQN');
    plot(x, aoi_data(3, :), 'g-^', 'LineWidth', 2, 'MarkerSize', 8, 'DisplayName', '演员-评论家');
    plot(x, aoi_data(4, :), 'k-x', 'LineWidth', 2, 'MarkerSize', 8, 'DisplayName', '固定功率');
    
    xlabel('场景');
    ylabel('AoI (ms)');
    title('AoI趋势对比');
    legend('Location', 'best');
    set(gca, 'XTickLabel', scenarios);
    grid on;
    
    % 保存测试图
    saveas(gcf, 'quick_test_line.png');
    fprintf('✓ 线图测试通过\n');
    
    % 输出数值结果
    fprintf('\n=== 数值结果 ===\n');
    overall_aoi = mean(aoi_data, 2);
    overall_energy = mean(energy_data, 2);
    
    fprintf('算法性能排序:\n');
    [~, aoi_rank] = sort(overall_aoi);
    [~, energy_rank] = sort(overall_energy);
    
    fprintf('AoI排序 (从低到高): ');
    for i = 1:4
        fprintf('%s ', algorithms{aoi_rank(i)});
    end
    fprintf('\n');
    
    fprintf('能耗排序 (从低到高): ');
    for i = 1:4
        fprintf('%s ', algorithms{energy_rank(i)});
    end
    fprintf('\n');
    
    fprintf('\n=== 快速测试完成 ===\n');
    fprintf('生成的文件:\n');
    fprintf('✓ quick_test_scatter.png\n');
    fprintf('✓ quick_test_bar.png\n');
    fprintf('✓ quick_test_line.png\n');
    
    fprintf('\n如果这个测试通过，可以尝试运行:\n');
    fprintf('demo_aoi_expected_results\n');
    
catch ME
    fprintf('❌ 快速测试失败:\n');
    fprintf('错误: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end
