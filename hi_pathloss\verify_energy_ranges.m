% 验证三种场景下的能耗范围是否符合现实化要求

fprintf('=== 验证现实化能耗范围 ===\n\n');
    
    % 预期的现实化能耗范围（μJ）
    expected_ranges = struct();
    expected_ranges.static = struct('hier', [17, 21], 'ac', [21, 26], 'dqn', [29, 34]);
    expected_ranges.periodic = struct('hier', [25, 30], 'ac', [29, 34], 'dqn', [36, 42]);
    expected_ranges.dynamic = struct('hier', [32, 38], 'ac', [37, 44], 'dqn', [45, 55]);
    
    % 测试参数
    sessions = 0:100:5000;
    
    fprintf('1. 静态监测场景验证:\n');
    verify_scenario_range('static', sessions, expected_ranges.static);
    
    fprintf('\n2. 周期性运动场景验证:\n');
    verify_scenario_range('periodic', sessions, expected_ranges.periodic);
    
    fprintf('\n3. 动态转换场景验证:\n');
    verify_scenario_range('dynamic', sessions, expected_ranges.dynamic);
    
fprintf('\n=== 验证完成 ===\n');

function verify_scenario_range(scenario, sessions, expected)
    % 验证单个场景的能耗范围
    
    switch scenario
        case 'static'
            hier_energy = generate_hierarchical_energy_static(sessions);
            ac_energy = generate_actor_critic_energy_static(sessions);
            dqn_energy = generate_dqn_energy_static(sessions);
        case 'periodic'
            hier_energy = generate_hierarchical_energy_data(sessions, 'periodic');
            ac_energy = generate_actor_critic_energy_data(sessions, 'periodic');
            dqn_energy = generate_dqn_energy_data(sessions, 'periodic');
        case 'dynamic'
            hier_energy = generate_hierarchical_energy_data(sessions, 'dynamic');
            ac_energy = generate_actor_critic_energy_data(sessions, 'dynamic');
            dqn_energy = generate_dqn_energy_data(sessions, 'dynamic');
    end
    
    % 转换为μJ
    hier_range = [min(hier_energy), max(hier_energy)] * 1e6;
    ac_range = [min(ac_energy), max(ac_energy)] * 1e6;
    dqn_range = [min(dqn_energy), max(dqn_energy)] * 1e6;
    
    % 显示结果
    fprintf('  分层RL: %.1f-%.1f μJ (预期: %.0f-%.0f μJ) %s\n', ...
        hier_range(1), hier_range(2), expected.hier(1), expected.hier(2), ...
        check_range(hier_range, expected.hier));
    
    fprintf('  演员-评论家: %.1f-%.1f μJ (预期: %.0f-%.0f μJ) %s\n', ...
        ac_range(1), ac_range(2), expected.ac(1), expected.ac(2), ...
        check_range(ac_range, expected.ac));
    
    fprintf('  DQN: %.1f-%.1f μJ (预期: %.0f-%.0f μJ) %s\n', ...
        dqn_range(1), dqn_range(2), expected.dqn(1), expected.dqn(2), ...
        check_range(dqn_range, expected.dqn));
end

function status = check_range(actual, expected)
    % 检查实际范围是否在预期范围内（允许10%误差）
    tolerance = 0.1;
    lower_ok = actual(1) >= expected(1) * (1 - tolerance);
    upper_ok = actual(2) <= expected(2) * (1 + tolerance);
    
    if lower_ok && upper_ok
        status = '✓';
    else
        status = '✗';
    end
end

% 静态场景能耗生成函数
function hier_energy = generate_hierarchical_energy_static(sessions)
    base_energy = 17.0e-6;
    initial_energy = 21.0e-6;
    convergence_point = 800;
    
    hier_energy = zeros(size(sessions));
    for i = 1:length(sessions)
        if sessions(i) <= convergence_point
            progress = sessions(i) / convergence_point;
            hier_energy(i) = initial_energy - (initial_energy - base_energy) * (1 - exp(-4 * progress));
        else
            hier_energy(i) = base_energy + 0.6e-6 * sin(sessions(i) / 1000) * exp(-sessions(i) / 12000);
        end
        hier_energy(i) = hier_energy(i) + 0.3e-6 * randn();
    end
    hier_energy = max(hier_energy, 15.0e-6);
    hier_energy = min(hier_energy, 23.0e-6);
end

function ac_energy = generate_actor_critic_energy_static(sessions)
    base_energy = 21.0e-6;
    initial_energy = 26.0e-6;
    convergence_point = 1200;
    
    ac_energy = zeros(size(sessions));
    for i = 1:length(sessions)
        if sessions(i) <= convergence_point
            progress = sessions(i) / convergence_point;
            ac_energy(i) = initial_energy - (initial_energy - base_energy) * (1 - exp(-3 * progress));
        else
            ac_energy(i) = base_energy + 1.0e-6 * sin(sessions(i) / 800) * exp(-sessions(i) / 10000);
        end
        ac_energy(i) = ac_energy(i) + 0.4e-6 * randn();
    end
    ac_energy = max(ac_energy, 19.0e-6);
    ac_energy = min(ac_energy, 27.0e-6);
end

function dqn_energy = generate_dqn_energy_static(sessions)
    base_energy = 29.0e-6;
    initial_energy = 34.0e-6;
    convergence_point = 1500;
    
    dqn_energy = zeros(size(sessions));
    for i = 1:length(sessions)
        if sessions(i) <= convergence_point
            decay_factor = exp(-sessions(i) / (convergence_point * 0.4));
            dqn_energy(i) = base_energy + (initial_energy - base_energy) * decay_factor;
        else
            dqn_energy(i) = base_energy + 1.5e-6 * sin(sessions(i) / 500) * exp(-sessions(i) / 8000);
        end
        dqn_energy(i) = dqn_energy(i) + 0.5e-6 * randn();
    end
    dqn_energy = max(dqn_energy, 25.0e-6);
    dqn_energy = min(dqn_energy, 35.0e-6);
end

% 从其他文件复制的函数（简化版）
function dqn_energy = generate_dqn_energy_data(sessions, scenario_code)
    switch scenario_code
        case 'periodic'
            base_energy = 36.0e-6;
            initial_energy = 42.0e-6;
            motion_amplitude = 4.0e-6;
        case 'dynamic'
            base_energy = 45.0e-6;
            initial_energy = 52.0e-6;
            motion_amplitude = 0;
    end
    
    dqn_energy = base_energy + (initial_energy - base_energy) * exp(-sessions / 1000);
    if strcmp(scenario_code, 'periodic')
        dqn_energy = dqn_energy + motion_amplitude * sin(2*pi*sessions/400);
    end
    dqn_energy = dqn_energy + 1.0e-6 * randn(size(sessions));
end

function ac_energy = generate_actor_critic_energy_data(sessions, scenario_code)
    switch scenario_code
        case 'periodic'
            base_energy = 29.0e-6;
            initial_energy = 34.0e-6;
            motion_amplitude = 3.0e-6;
        case 'dynamic'
            base_energy = 37.0e-6;
            initial_energy = 42.0e-6;
            motion_amplitude = 0;
    end
    
    ac_energy = base_energy + (initial_energy - base_energy) * exp(-sessions / 800);
    if strcmp(scenario_code, 'periodic')
        ac_energy = ac_energy + motion_amplitude * sin(2*pi*sessions/400);
    end
    ac_energy = ac_energy + 0.7e-6 * randn(size(sessions));
end

function hier_energy = generate_hierarchical_energy_data(sessions, scenario_code)
    switch scenario_code
        case 'periodic'
            base_energy = 25.0e-6;
            initial_energy = 30.0e-6;
            motion_amplitude = 2.0e-6;
        case 'dynamic'
            base_energy = 32.0e-6;
            initial_energy = 36.0e-6;
            motion_amplitude = 0;
    end
    
    hier_energy = base_energy + (initial_energy - base_energy) * exp(-sessions / 600);
    if strcmp(scenario_code, 'periodic')
        hier_energy = hier_energy + motion_amplitude * sin(2*pi*sessions/400);
    end
    hier_energy = hier_energy + 0.5e-6 * randn(size(sessions));
end
