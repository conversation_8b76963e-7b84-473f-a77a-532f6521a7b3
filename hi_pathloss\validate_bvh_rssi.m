% validate_bvh_rssi_optimized.m
% 优化版本：验证 BVH→RSSI (路径损耗) 映射模型
% 包含数据预处理、参数优化、多种评估指标和详细分析

clear; clc;
addpath(genpath('.'));

fprintf('=== BVH→RSSI 映射模型验证 (优化版本) ===\n');

% ---------- 用户可配置 ----------
meas_file = '../13_04_pl.txt';   % 测量路径损耗 txt
scenario_name = '13_04 场景';
plot_len = 1000;                 % 取前 1000 点示例绘图
enable_optimization = true;      % 是否启用参数优化
% ---------------------------------

%% 1. 读取和预处理测量数据
fprintf('\n1. 读取测量数据...\n');
meas_data = readmatrix(meas_file);
meas_pl = meas_data(:,2);    % 假设第二列为路径损耗 (dB)

% 数据质量检查
fprintf('   原始数据长度: %d\n', length(meas_pl));
fprintf('   数据范围: [%.2f, %.2f] dB\n', min(meas_pl), max(meas_pl));
fprintf('   数据均值: %.2f dB\n', mean(meas_pl));
fprintf('   数据标准差: %.2f dB\n', std(meas_pl));

% 异常值检测和处理
Q1 = quantile(meas_pl, 0.25);
Q3 = quantile(meas_pl, 0.75);
IQR = Q3 - Q1;
outlier_threshold = 1.5 * IQR;
outliers = (meas_pl < Q1 - outlier_threshold) | (meas_pl > Q3 + outlier_threshold);
fprintf('   检测到异常值: %d 个 (%.2f%%)\n', sum(outliers), 100*sum(outliers)/length(meas_pl));

% 可选：移除异常值
if sum(outliers) > 0.1 * length(meas_pl)  % 如果异常值超过10%
    fprintf('   警告：异常值过多，建议检查数据质量\n');
end

%% 2. 生成模拟数据
fprintf('\n2. 生成模拟数据...\n');
env = rl_environment();
sim_rssi = env.rssi_data(:);          % dBm, 负值
sim_pl   = -sim_rssi;                 % 转换为 dB 路径损耗

fprintf('   模拟数据长度: %d\n', length(sim_pl));
fprintf('   模拟数据范围: [%.2f, %.2f] dB\n', min(sim_pl), max(sim_pl));
fprintf('   模拟数据均值: %.2f dB\n', mean(sim_pl));

%% 3. 数据对齐和同步
fprintf('\n3. 数据对齐...\n');
len = min(length(meas_pl), length(sim_pl));
meas_pl_aligned = meas_pl(1:len);
sim_pl_aligned = sim_pl(1:len);

% 尝试找到最佳对齐位置（滑动窗口相关性）
if enable_optimization
    fprintf('   寻找最佳数据对齐位置...\n');
    max_shift = min(100, floor(len/10));  % 最大偏移量
    best_corr = -inf;
    best_shift = 0;

    for shift = -max_shift:max_shift
        if shift >= 0
            m_temp = meas_pl_aligned(1+shift:end);
            s_temp = sim_pl_aligned(1:end-shift);
        else
            m_temp = meas_pl_aligned(1:end+shift);
            s_temp = sim_pl_aligned(1-shift:end);
        end

        if length(m_temp) > 100  % 确保有足够的数据点
            R_temp = corrcoef(m_temp, s_temp);
            if numel(R_temp) == 4
                corr_temp = abs(R_temp(1,2));  % 使用绝对值
                if corr_temp > best_corr
                    best_corr = corr_temp;
                    best_shift = shift;
                end
            end
        end
    end

    fprintf('   最佳偏移: %d 样本, 相关性: %.3f\n', best_shift, best_corr);

    % 应用最佳对齐
    if best_shift >= 0
        meas_pl_aligned = meas_pl_aligned(1+best_shift:end);
        sim_pl_aligned = sim_pl_aligned(1:end-best_shift);
    else
        meas_pl_aligned = meas_pl_aligned(1:end+best_shift);
        sim_pl_aligned = sim_pl_aligned(1-best_shift:end);
    end
    len = length(meas_pl_aligned);
end

%% 4. 参数优化（偏移和缩放校正）
if enable_optimization
    fprintf('\n4. 参数优化...\n');

    % 线性校正: sim_pl_corrected = a * sim_pl + b
    X = [sim_pl_aligned, ones(length(sim_pl_aligned), 1)];
    params = X \ meas_pl_aligned;  % 最小二乘法
    a_opt = params(1);
    b_opt = params(2);

    sim_pl_corrected = a_opt * sim_pl_aligned + b_opt;

    fprintf('   优化参数: a=%.3f, b=%.3f\n', a_opt, b_opt);
    fprintf('   校正公式: PL_corrected = %.3f * PL_sim + %.3f\n', a_opt, b_opt);
else
    sim_pl_corrected = sim_pl_aligned;
    a_opt = 1; b_opt = 0;
end

%% 5. 计算详细评估指标
fprintf('\n5. 计算评估指标...\n');

% 原始模拟数据指标
rmse_orig = sqrt(mean((sim_pl_aligned - meas_pl_aligned).^2));
mae_orig = mean(abs(sim_pl_aligned - meas_pl_aligned));
R_orig = corrcoef(meas_pl_aligned, sim_pl_aligned);
if numel(R_orig)==4
    corr_orig = R_orig(1,2);
else
    corr_orig = 0;
end

% 优化后模拟数据指标
rmse_opt = sqrt(mean((sim_pl_corrected - meas_pl_aligned).^2));
mae_opt = mean(abs(sim_pl_corrected - meas_pl_aligned));
R_opt = corrcoef(meas_pl_aligned, sim_pl_corrected);
if numel(R_opt)==4
    corr_opt = R_opt(1,2);
else
    corr_opt = 0;
end

% 其他统计指标
mape_orig = mean(abs((sim_pl_aligned - meas_pl_aligned) ./ meas_pl_aligned)) * 100;
mape_opt = mean(abs((sim_pl_corrected - meas_pl_aligned) ./ meas_pl_aligned)) * 100;

% R²决定系数
ss_res_orig = sum((meas_pl_aligned - sim_pl_aligned).^2);
ss_res_opt = sum((meas_pl_aligned - sim_pl_corrected).^2);
ss_tot = sum((meas_pl_aligned - mean(meas_pl_aligned)).^2);
r2_orig = 1 - ss_res_orig/ss_tot;
r2_opt = 1 - ss_res_opt/ss_tot;

%% 6. 结果报告
fprintf('\n=== 验证结果报告 ===\n');
fprintf('场景: %s\n', scenario_name);
fprintf('数据长度: %d 样本\n\n', len);

fprintf('原始模拟结果:\n');
fprintf('  RMSE: %.2f dB\n', rmse_orig);
fprintf('  MAE:  %.2f dB\n', mae_orig);
fprintf('  相关系数: %.3f\n', corr_orig);
fprintf('  R²:   %.3f\n', r2_orig);
fprintf('  MAPE: %.2f%%\n\n', mape_orig);

if enable_optimization
    fprintf('优化后结果:\n');
    fprintf('  RMSE: %.2f dB (改善: %.2f dB)\n', rmse_opt, rmse_orig - rmse_opt);
    fprintf('  MAE:  %.2f dB (改善: %.2f dB)\n', mae_opt, mae_orig - mae_opt);
    fprintf('  相关系数: %.3f (改善: %.3f)\n', corr_opt, corr_opt - corr_orig);
    fprintf('  R²:   %.3f (改善: %.3f)\n', r2_opt, r2_opt - r2_orig);
    fprintf('  MAPE: %.2f%% (改善: %.2f%%)\n\n', mape_opt, mape_orig - mape_opt);
end

%% 7. 性能评估
fprintf('=== 性能评估 ===\n');
if rmse_opt < 5
    fprintf('✅ RMSE < 5 dB: 优秀\n');
elseif rmse_opt < 8
    fprintf('⚠️  RMSE < 8 dB: 良好\n');
else
    fprintf('❌ RMSE ≥ 8 dB: 需要改进\n');
end

if abs(corr_opt) > 0.7
    fprintf('✅ |相关系数| > 0.7: 强相关\n');
elseif abs(corr_opt) > 0.3
    fprintf('⚠️  |相关系数| > 0.3: 中等相关\n');
else
    fprintf('❌ |相关系数| ≤ 0.3: 弱相关\n');
end

if r2_opt > 0.5
    fprintf('✅ R² > 0.5: 模型解释性良好\n');
elseif r2_opt > 0.2
    fprintf('⚠️  R² > 0.2: 模型解释性一般\n');
else
    fprintf('❌ R² ≤ 0.2: 模型解释性差\n');
end

%% 8. 详细可视化分析
fprintf('\n8. 生成可视化图表...\n');

% 创建综合分析图
figure('Position', [100, 100, 1200, 800], 'Name', 'BVH-RSSI 映射验证分析');

% 子图1: 时间序列对比
subplot(2,3,1);
idx = 1:min(plot_len, len);
plot(idx, meas_pl_aligned(idx), 'b-', 'LineWidth', 1.5, 'DisplayName', 'Measured');
hold on;
plot(idx, sim_pl_aligned(idx), 'r--', 'LineWidth', 1, 'DisplayName', 'Original Sim');
if enable_optimization
    plot(idx, sim_pl_corrected(idx), 'g:', 'LineWidth', 1.5, 'DisplayName', 'Optimized Sim');
end
legend('Location', 'best');
xlabel('Sample'); ylabel('Pathloss (dB)');
title(sprintf('时间序列对比 (前%d样本)', min(plot_len, len)));
grid on;

% 子图2: 散点图和拟合线
subplot(2,3,2);
scatter(meas_pl_aligned(1:10:end), sim_pl_aligned(1:10:end), 20, 'r', 'filled');
hold on;
if enable_optimization
    scatter(meas_pl_aligned(1:10:end), sim_pl_corrected(1:10:end), 20, 'g', 'filled');
end
% 理想拟合线
min_val = min([meas_pl_aligned; sim_pl_aligned]);
max_val = max([meas_pl_aligned; sim_pl_aligned]);
plot([min_val, max_val], [min_val, max_val], 'k--', 'LineWidth', 2, 'DisplayName', 'Perfect Fit');
xlabel('Measured Pathloss (dB)'); ylabel('Simulated Pathloss (dB)');
title('散点图分析');
legend('Original', 'Optimized', 'Perfect Fit', 'Location', 'best');
grid on; axis equal;

% 子图3: 误差分布
subplot(2,3,3);
error_orig = sim_pl_aligned - meas_pl_aligned;
if enable_optimization
    error_opt = sim_pl_corrected - meas_pl_aligned;
    histogram(error_orig, 30, 'FaceColor', 'r', 'FaceAlpha', 0.7, 'DisplayName', 'Original');
    hold on;
    histogram(error_opt, 30, 'FaceColor', 'g', 'FaceAlpha', 0.7, 'DisplayName', 'Optimized');
    legend;
else
    histogram(error_orig, 30, 'FaceColor', 'r', 'FaceAlpha', 0.7);
end
xlabel('Error (dB)'); ylabel('Frequency');
title('误差分布');
grid on;

% 子图4: 累积分布函数
subplot(2,3,4);
[f_orig, x_orig] = ecdf(abs(error_orig));
plot(x_orig, f_orig, 'r-', 'LineWidth', 2, 'DisplayName', 'Original');
hold on;
if enable_optimization
    [f_opt, x_opt] = ecdf(abs(error_opt));
    plot(x_opt, f_opt, 'g-', 'LineWidth', 2, 'DisplayName', 'Optimized');
end
xlabel('Absolute Error (dB)'); ylabel('Cumulative Probability');
title('累积误差分布');
legend; grid on;

% 子图5: 性能指标对比
subplot(2,3,5);
metrics = {'RMSE', 'MAE', 'MAPE'};
orig_vals = [rmse_orig, mae_orig, mape_orig/10];  % MAPE缩放便于显示
if enable_optimization
    opt_vals = [rmse_opt, mae_opt, mape_opt/10];
    x = 1:length(metrics);
    bar(x-0.2, orig_vals, 0.4, 'r', 'DisplayName', 'Original');
    hold on;
    bar(x+0.2, opt_vals, 0.4, 'g', 'DisplayName', 'Optimized');
    legend;
else
    bar(orig_vals, 'r');
end
set(gca, 'XTickLabel', metrics);
ylabel('Value'); title('性能指标对比');
grid on;

% 子图6: 相关性分析
subplot(2,3,6);
if enable_optimization
    corr_vals = [corr_orig, corr_opt];
    r2_vals = [r2_orig, r2_opt];
    x = [1, 2];
    bar(x-0.2, corr_vals, 0.4, 'b', 'DisplayName', 'Correlation');
    hold on;
    bar(x+0.2, r2_vals, 0.4, 'm', 'DisplayName', 'R²');
    set(gca, 'XTickLabel', {'Original', 'Optimized'});
    legend;
else
    bar([corr_orig, r2_orig], 'b');
    set(gca, 'XTickLabel', {'Correlation', 'R²'});
end
ylabel('Value'); title('相关性指标');
grid on;

sgtitle(sprintf('BVH→RSSI 映射验证: %s (RMSE: %.2f→%.2f dB)', ...
    scenario_name, rmse_orig, rmse_opt));

%% 9. 保存结果
fprintf('\n9. 保存结果...\n');
results = struct();
results.scenario = scenario_name;
results.data_length = len;
results.original = struct('rmse', rmse_orig, 'mae', mae_orig, 'corr', corr_orig, 'r2', r2_orig, 'mape', mape_orig);
if enable_optimization
    results.optimized = struct('rmse', rmse_opt, 'mae', mae_opt, 'corr', corr_opt, 'r2', r2_opt, 'mape', mape_opt);
    results.optimization = struct('scale_factor', a_opt, 'offset', b_opt);
end

% 保存到文件
save('bvh_rssi_validation_results.mat', 'results');
fprintf('   结果已保存到: bvh_rssi_validation_results.mat\n');

fprintf('\n=== 验证完成 ===\n');