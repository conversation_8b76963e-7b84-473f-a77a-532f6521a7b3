% ablation_experiments.m
% 运行三组消融实验并输出结果到控制台
clear; clc;
addpath(genpath('.'));

% 场景示例 (静态)
scenario = struct('name','静态测试','type','static');

% 基础参数配置
base_params = struct(...
    'num_episodes',40,...
    'max_steps_per_episode',200,...
    'batch_size',32,...
    'update_frequency',10,...
    'learning_rate',0.001,...
    'gamma',0.95,...
    'epsilon',1.0,...
    'epsilon_decay',0.995,...
    'epsilon_min',0.05);

%% 1) 仅下层 DQN vs 分层 RL
fprintf('\n=== 消融 1: 下层 DQN vs 分层 RL ===\n');
env = rl_environment();
[agent_dqn,res_dqn] = train_dqn(env,base_params);
[agent_hrl,res_hrl] = train_hierarchical_rl(env,scenario);
fprintf('DQN AvgReward %.2f | Hierarchical RL AvgReward %.2f\n', ...
        res_dqn.final_avg_reward, res_hrl.final_avg_reward);

%% 2) 不同 Manager hidden size
fprintf('\n=== 消融 2: 不同 Manager hidden size ===\n');
hsizes = [32,64,128];
for h = hsizes
    env_h = rl_environment();
    % 使用evalin在base workspace中设置变量
    evalin('base', sprintf('HRL_HIDDEN_SIZE = %d;', h));
    [~, res_h] = train_hierarchical_rl(env_h, scenario);
    fprintf('Hidden %3d -> AvgReward %.2f\n', h, res_h.final_avg_reward);
end
% 清理变量
if evalin('base', 'exist("HRL_HIDDEN_SIZE", "var")')
    evalin('base', 'clear HRL_HIDDEN_SIZE');
end

%% 3) 无 RSSI / 无 IMU 特征
fprintf('\n=== 消融 3: 特征遮蔽 ===\n');
env_no_rssi = make_mask_env('rssi');
[~,res_rssi] = train_hierarchical_rl(env_no_rssi,scenario);

env_no_imu  = make_mask_env('imu');
[~,res_imu] = train_hierarchical_rl(env_no_imu,scenario);

fprintf('无 RSSI AvgReward %.2f | 无 IMU AvgReward %.2f | 原始 %.2f\n', ...
        res_rssi.final_avg_reward, res_imu.final_avg_reward, res_hrl.final_avg_reward);

%% --------- 辅助函数 --------
function env_mask = make_mask_env(flag)
    base_env = rl_environment();
    env_mask = base_env; % value copy

    % 保存原始函数句柄
    if isfield(base_env, 'reset') && isa(base_env.reset, 'function_handle')
        original_reset = base_env.reset;
    else
        original_reset = @() base_env.reset();
    end

    if isfield(base_env, 'step') && isa(base_env.step, 'function_handle')
        original_step = base_env.step;
    else
        original_step = @(action) base_env.step(action);
    end

    if isfield(base_env, 'get_current_state') && isa(base_env.get_current_state, 'function_handle')
        original_get_state = base_env.get_current_state;
    else
        original_get_state = @() base_env.get_current_state();
    end

    % 定义新的 get_current_state 函数
    function s = new_get_state()
        s = original_get_state();
        % 根据环境状态维度调整遮蔽策略
        switch flag
            case 'rssi'
                % 假设RSSI在状态向量的第4个位置
                if length(s) >= 4
                    s(4) = 0;
                end
            case 'imu'
                % 假设IMU数据在状态向量的第1和第5个位置
                if length(s) >= 1
                    s(1) = 0;
                end
                if length(s) >= 5
                    s(5) = 0;
                end
        end
    end

    % 重新定义reset函数以使用新的状态获取
    function s = new_reset()
        s = original_reset();
        s = new_get_state(); % 应用遮蔽
    end

    % 重新定义step函数以使用新的状态获取
    function [next_state, reward, done, info] = new_step(action)
        [next_state, reward, done, info] = original_step(action);
        next_state = new_get_state(); % 应用遮蔽
    end

    % 更新环境函数
    env_mask.get_current_state = @new_get_state;
    env_mask.reset = @new_reset;
    env_mask.step = @new_step;
end