% validate_bvh_rssi_advanced.m
% 高级优化版本：包含机器学习校正和多种优化策略

clear; clc;
addpath(genpath('.'));

fprintf('=== BVH→RSSI 映射高级优化验证 ===\n');

% 配置参数
meas_file = '../13_04_pl.txt';
scenario_name = '13_04 场景 (高级优化)';

%% 1. 数据加载和预处理
fprintf('\n1. 高级数据预处理...\n');
meas_data = readmatrix(meas_file);
meas_pl = meas_data(:,2);

% 数据平滑处理
window_size = 5;
meas_pl_smooth = movmean(meas_pl, window_size);
fprintf('   应用 %d 点移动平均平滑\n', window_size);

% 生成模拟数据
env = rl_environment();
sim_rssi = env.rssi_data(:);
sim_pl = -sim_rssi;

%% 2. 多种对齐策略
fprintf('\n2. 多种对齐策略测试...\n');
len = min(length(meas_pl_smooth), length(sim_pl));
meas_aligned = meas_pl_smooth(1:len);
sim_aligned = sim_pl(1:len);

% 策略1: 互相关对齐
max_lag = min(200, floor(len/10));
[correlation, lags] = xcorr(meas_aligned, sim_aligned, max_lag);
[~, max_idx] = max(abs(correlation));
best_lag = lags(max_idx);

fprintf('   互相关最佳滞后: %d 样本\n', best_lag);

% 应用对齐
if best_lag > 0
    meas_final = meas_aligned(1+best_lag:end);
    sim_final = sim_aligned(1:end-best_lag);
elseif best_lag < 0
    meas_final = meas_aligned(1:end+best_lag);
    sim_final = sim_aligned(1-best_lag:end);
else
    meas_final = meas_aligned;
    sim_final = sim_aligned;
end

%% 3. 机器学习校正
fprintf('\n3. 机器学习校正...\n');

% 特征工程
len_final = length(meas_final);
features = zeros(len_final, 5);
features(:,1) = sim_final;                           % 原始模拟值
features(:,2) = movmean(sim_final, 3);              % 3点平均
features(:,3) = [0; diff(sim_final)];               % 一阶差分
features(:,4) = [0; 0; diff(sim_final, 2)];        % 二阶差分
features(:,5) = (1:len_final)' / len_final;         % 时间特征

% 分割训练和测试集
train_ratio = 0.7;
train_size = floor(len_final * train_ratio);
train_idx = 1:train_size;
test_idx = (train_size+1):len_final;

X_train = features(train_idx, :);
y_train = meas_final(train_idx);
X_test = features(test_idx, :);
y_test = meas_final(test_idx);

% 多项式回归 (2次)
degree = 2;
X_train_poly = [X_train, X_train.^2];
X_test_poly = [X_test, X_test.^2];

% 正则化线性回归
lambda = 0.01;
I = eye(size(X_train_poly, 2));
theta = (X_train_poly' * X_train_poly + lambda * I) \ (X_train_poly' * y_train);

% 预测
y_pred_train = X_train_poly * theta;
y_pred_test = X_test_poly * theta;

% 全数据预测
X_full_poly = [features, features.^2];
sim_ml_corrected = X_full_poly * theta;

fprintf('   使用 %d 次多项式回归 + L2正则化 (λ=%.3f)\n', degree, lambda);

%% 4. 多种评估指标
fprintf('\n4. 综合评估...\n');

% 原始结果
rmse_orig = sqrt(mean((sim_final - meas_final).^2));
mae_orig = mean(abs(sim_final - meas_final));
R_orig = corrcoef(meas_final, sim_final);
corr_orig = R_orig(1,2);

% 线性校正结果 (简单)
X_simple = [sim_final, ones(length(sim_final), 1)];
params_simple = X_simple \ meas_final;
sim_linear = X_simple * params_simple;

rmse_linear = sqrt(mean((sim_linear - meas_final).^2));
mae_linear = mean(abs(sim_linear - meas_final));
R_linear = corrcoef(meas_final, sim_linear);
corr_linear = R_linear(1,2);

% ML校正结果
rmse_ml = sqrt(mean((sim_ml_corrected - meas_final).^2));
mae_ml = mean(abs(sim_ml_corrected - meas_final));
R_ml = corrcoef(meas_final, sim_ml_corrected);
corr_ml = R_ml(1,2);

% 测试集性能
rmse_test = sqrt(mean((y_pred_test - y_test).^2));
mae_test = mean(abs(y_pred_test - y_test));
R_test = corrcoef(y_test, y_pred_test);
corr_test = R_test(1,2);

%% 5. 结果报告
fprintf('\n=== 高级优化结果报告 ===\n');
fprintf('场景: %s\n', scenario_name);
fprintf('数据长度: %d 样本 (训练: %d, 测试: %d)\n\n', len_final, train_size, length(test_idx));

fprintf('📊 性能对比:\n');
fprintf('方法                RMSE(dB)  MAE(dB)   相关系数\n');
fprintf('原始模拟            %.2f      %.2f      %.3f\n', rmse_orig, mae_orig, corr_orig);
fprintf('线性校正            %.2f      %.2f      %.3f\n', rmse_linear, mae_linear, corr_linear);
fprintf('机器学习校正        %.2f      %.2f      %.3f\n', rmse_ml, mae_ml, corr_ml);
fprintf('测试集性能          %.2f      %.2f      %.3f\n\n', rmse_test, mae_test, corr_test);

% 改善分析
fprintf('📈 改善分析:\n');
fprintf('线性校正 vs 原始:   %.1f%%     %.1f%%     %.1f%%\n', ...
    (rmse_orig-rmse_linear)/rmse_orig*100, ...
    (mae_orig-mae_linear)/mae_orig*100, ...
    (corr_linear-corr_orig)/abs(corr_orig)*100);
fprintf('ML校正 vs 原始:     %.1f%%     %.1f%%     %.1f%%\n', ...
    (rmse_orig-rmse_ml)/rmse_orig*100, ...
    (mae_orig-mae_ml)/mae_orig*100, ...
    (corr_ml-corr_orig)/abs(corr_orig)*100);

%% 6. 性能等级评估
fprintf('\n🎯 最终性能等级:\n');
final_rmse = rmse_ml;
final_corr = abs(corr_ml);

if final_rmse < 3 && final_corr > 0.8
    grade = 'A+';
    desc = '优秀 - 可直接用于高精度应用';
elseif final_rmse < 5 && final_corr > 0.6
    grade = 'A';
    desc = '良好 - 适合大多数应用场景';
elseif final_rmse < 7 && final_corr > 0.4
    grade = 'B';
    desc = '可接受 - 需要进一步优化';
elseif final_rmse < 10 && final_corr > 0.2
    grade = 'C';
    desc = '一般 - 仅适合粗略估计';
else
    grade = 'D';
    desc = '差 - 需要重新设计模型';
end

fprintf('   等级: %s\n', grade);
fprintf('   评价: %s\n', desc);
fprintf('   RMSE: %.2f dB, 相关性: %.3f\n', final_rmse, final_corr);

%% 7. 保存高级结果
results_advanced = struct();
results_advanced.scenario = scenario_name;
results_advanced.data_length = len_final;
results_advanced.original = struct('rmse', rmse_orig, 'mae', mae_orig, 'corr', corr_orig);
results_advanced.linear = struct('rmse', rmse_linear, 'mae', mae_linear, 'corr', corr_linear);
results_advanced.ml = struct('rmse', rmse_ml, 'mae', mae_ml, 'corr', corr_ml);
results_advanced.test = struct('rmse', rmse_test, 'mae', mae_test, 'corr', corr_test);
results_advanced.grade = grade;

save('bvh_rssi_advanced_results.mat', 'results_advanced');
fprintf('\n💾 高级结果已保存到: bvh_rssi_advanced_results.mat\n');

fprintf('\n=== 高级验证完成 ===\n');
