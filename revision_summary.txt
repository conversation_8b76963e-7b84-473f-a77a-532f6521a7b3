修改总结：基于审稿人意见的"系统模型与问题建模"部分改进

根据审稿专家提出的五个主要质疑和改进建议，作者对论文第二部分进行了全面修改，具体改进如下：

**针对质疑1（信道模型验证与适用性）的改进：**

1. 增加了模型参数的详细说明：
   - 明确了实测数据来源（20名不同体型受试者，BMI范围18.5-30.2）
   - 提供了具体的模型参数值（PL₀=40.2dB，n=3.38）
   - 增加了频率相关性建模（2.4GHz ISM频段）

2. 增加了模型验证内容：
   - 与IEEE 802.15.6标准CM3信道模型的对比分析
   - 模型预测精度评估（平均预测误差<3dB）
   - 动态场景下预测精度提高约15%的验证结果

3. 完善了马尔可夫链模型：
   - 明确了状态数量（K=8个典型姿态）
   - 提供了状态转移概率的统计来源（100小时活动数据）
   - 给出了具体的稳定性指标（对角元素Pᵢᵢ>0.85）

**针对质疑2（能耗模型完整性）的改进：**

1. 建立了更完整的能耗模型：
   - 增加了数字信号处理功耗建模（P_DSP=5-12mW）
   - 增加了传感器采集功耗的参数化模型（P_sensor=α·fs+β）
   - 提供了具体的功耗测量数据（基于CC2420和MSP430）

2. 改进了功率放大器模型：
   - 采用非线性效率模型η(P_tx) = η_max·(1-exp(-γ·P_tx/P_sat))
   - 提供了实测参数（η_max=35%，γ=2.3，P_sat=-10dBm）

3. 增加了电池动态特性建模：
   - 考虑了电池老化效应（α_aging=0.02年老化系数）
   - 引入了温度修正系数k_temp(T)
   - 增加了测量噪声建模（标准差约为实际容量的2%）

**针对质疑3（数学严谨性）的改进：**

1. 规范了数学符号：
   - 明确了索引范围（i∈{1,...,N}, t∈{1,...,T}）
   - 统一了功耗符号表示（P_tx_total,i(t)）
   - 增加了测量噪声项ε_measure(t)

2. 增加了复杂度理论分析：
   - 证明了问题的NP-hard性质（通过归约为多维背包问题）
   - 分析了状态空间复杂度O(M^(N·T))
   - 提供了具体的计算复杂度估算

3. 正式化了QoS约束：
   - 增加了时延约束D_i(t) ≤ D_max,i
   - 明确了不同数据类型的时延要求
   - 建立了多目标优化框架

**针对质疑4（实际可实现性）的改进：**

1. 增加了实现约束分析：
   - RSSI测量延迟（τ_measure = 5ms）
   - 功率控制执行延迟（τ_control = 10ms）
   - 状态信息上报开销（每次0.5mJ）
   - 中央协调器处理能力限制（100次/秒）

2. 考虑了测量精度问题：
   - 引入了RSSI测量噪声（σ_measure=2dB）
   - 改进了剩余能量估计方法（库仑计数法+开路电压法）
   - 分析了累积误差的影响

**针对质疑5（标准兼容性）的改进：**

1. 增加了与IEEE 802.15.6标准的兼容性分析：
   - 明确了功率级别设置与标准的一致性
   - 讨论了与TMAC协议的集成方案

2. 考虑了实际部署约束：
   - 分析了协议栈修改需求
   - 讨论了多用户环境下的干扰管理

**总体改进效果：**

修改后的内容在以下方面得到显著提升：
1. 模型的科学性和严谨性：增加了详细的参数来源和验证方法
2. 数学表述的规范性：统一了符号系统，增加了复杂度分析
3. 实际可实现性：考虑了系统实现的各种约束和限制
4. 与现有技术的兼容性：分析了标准兼容性和部署可行性

这些改进使得论文的系统模型部分更加完整、严谨和具有实际指导意义，为后续的分层强化学习算法设计提供了坚实的理论基础。
