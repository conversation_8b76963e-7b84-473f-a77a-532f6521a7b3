无线体域网（WBAN）在医疗监测、健康管理等场景中对低功耗且可靠的数据传输提出了严格要求，但人体信道的多时间尺度变化特性使传统功率控制方法难以兼顾能耗与链路质量。为此，本文提出一种基于Options-DQN的分层强化学习功率控制方法：高层元控制器（Meta-Controller）基于人体姿态特征和信道统计特性选择最优子策略（Options），实现O(log|S|)的状态空间压缩；低层选项控制器（Option-Controller）在选定时间窗口内执行具体的功率级别决策，通过自适应奖励塑形机制将长期网络寿命目标分解为短期子目标，解决传统RL的稀疏奖励问题。

我们在 Castalia-3.2-Augment 仿真平台上，结合真实人体路径损耗数据（13_01、13_04、35_01 数据集），构建了静态监测、周期性运动与动态转换三种典型场景。通过50次独立实验的统计分析，与六种对比方法（固定功率、RSSI门限控制、单层DQN、博弈论方法、凸优化方法、模糊逻辑控制）相比，所提Options-DQN方法在三类场景中平均能耗降低25.7%±2.1%（95%置信区间），包成功率提高6.3%±1.2%，网络寿命延长21.4%±3.8%。算法收敛时间为传统单层DQN的1/3（平均3.2×10^5步），在场景切换时的适应时间缩短至2-5分钟。
 
本文工作的主要贡献如下：1）首次将Options框架引入WBAN功率控制，实现了状态空间的对数级压缩和多时间尺度决策的统一；2）提出了基于网络寿命预测的自适应奖励塑形机制，解决了长期优化目标的稀疏奖励问题；3）设计了跨场景的元学习策略迁移方法，使算法能够在2-5分钟内适应新的人体姿态模式。该方法在计算复杂度为O(K×log|S|)（K为子策略数量）的前提下，实现了接近最优解的性能，为资源受限的WBAN环境提供了实用的智能功率控制解决方案。方法的局限性在于需要预先定义子策略集合，且在极端动态场景下的性能有待进一步验证。 