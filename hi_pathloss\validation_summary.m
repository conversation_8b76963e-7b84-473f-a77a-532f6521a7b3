% validation_summary.m
% BVH→RSSI 映射验证结果总结和分析

clear; clc;

fprintf('=== BVH→RSSI 映射验证结果分析 ===\n\n');

% 加载验证结果
if exist('bvh_rssi_validation_results.mat', 'file')
    load('bvh_rssi_validation_results.mat');
    
    fprintf('📊 验证场景: %s\n', results.scenario);
    fprintf('📈 数据长度: %d 样本\n\n', results.data_length);
    
    % 原始结果
    fprintf('🔴 原始模拟结果:\n');
    fprintf('   RMSE: %.2f dB\n', results.original.rmse);
    fprintf('   MAE:  %.2f dB\n', results.original.mae);
    fprintf('   相关系数: %.3f\n', results.original.corr);
    fprintf('   R²: %.3f\n', results.original.r2);
    fprintf('   MAPE: %.2f%%\n\n', results.original.mape);
    
    % 优化结果
    if isfield(results, 'optimized')
        fprintf('🟢 优化后结果:\n');
        fprintf('   RMSE: %.2f dB (改善: %.2f dB, %.1f%%)\n', ...
            results.optimized.rmse, ...
            results.original.rmse - results.optimized.rmse, ...
            (results.original.rmse - results.optimized.rmse) / results.original.rmse * 100);
        fprintf('   MAE:  %.2f dB (改善: %.2f dB, %.1f%%)\n', ...
            results.optimized.mae, ...
            results.original.mae - results.optimized.mae, ...
            (results.original.mae - results.optimized.mae) / results.original.mae * 100);
        fprintf('   相关系数: %.3f (改善: %.3f)\n', ...
            results.optimized.corr, ...
            results.optimized.corr - results.original.corr);
        fprintf('   R²: %.3f (改善: %.3f)\n', ...
            results.optimized.r2, ...
            results.optimized.r2 - results.original.r2);
        fprintf('   MAPE: %.2f%% (改善: %.2f%%, %.1f%%)\n\n', ...
            results.optimized.mape, ...
            results.original.mape - results.optimized.mape, ...
            (results.original.mape - results.optimized.mape) / results.original.mape * 100);
        
        % 优化参数
        fprintf('⚙️ 优化参数:\n');
        fprintf('   缩放因子: %.3f\n', results.optimization.scale_factor);
        fprintf('   偏移量: %.3f dB\n', results.optimization.offset);
        fprintf('   校正公式: PL_corrected = %.3f × PL_sim + %.3f\n\n', ...
            results.optimization.scale_factor, results.optimization.offset);
    end
    
    % 性能评估
    fprintf('📋 性能评估:\n');
    final_rmse = results.optimized.rmse;
    final_corr = abs(results.optimized.corr);
    final_r2 = results.optimized.r2;
    
    if final_rmse < 5
        fprintf('   ✅ RMSE < 5 dB: 优秀\n');
    elseif final_rmse < 8
        fprintf('   ⚠️  RMSE < 8 dB: 良好\n');
    else
        fprintf('   ❌ RMSE ≥ 8 dB: 需要改进\n');
    end
    
    if final_corr > 0.7
        fprintf('   ✅ |相关系数| > 0.7: 强相关\n');
    elseif final_corr > 0.3
        fprintf('   ⚠️  |相关系数| > 0.3: 中等相关\n');
    else
        fprintf('   ❌ |相关系数| ≤ 0.3: 弱相关\n');
    end
    
    if final_r2 > 0.5
        fprintf('   ✅ R² > 0.5: 模型解释性良好\n');
    elseif final_r2 > 0.2
        fprintf('   ⚠️  R² > 0.2: 模型解释性一般\n');
    else
        fprintf('   ❌ R² ≤ 0.2: 模型解释性差\n');
    end
    
    fprintf('\n');
    
    % 总体评价
    fprintf('🎯 总体评价:\n');
    score = 0;
    if final_rmse < 8, score = score + 1; end
    if final_corr > 0.3, score = score + 1; end
    if final_r2 > 0.2, score = score + 1; end
    
    switch score
        case 3
            fprintf('   🌟 优秀: 模型性能良好，可用于实际应用\n');
        case 2
            fprintf('   👍 良好: 模型性能可接受，建议进一步优化\n');
        case 1
            fprintf('   ⚠️  一般: 模型性能有限，需要显著改进\n');
        case 0
            fprintf('   ❌ 差: 模型性能不佳，需要重新设计\n');
    end
    
else
    fprintf('❌ 未找到验证结果文件，请先运行 validate_bvh_rssi.m\n');
end

fprintf('\n=== 分析完成 ===\n');
