function ok = test_mean_ci()
    % 测试 mean_ci 函数的正确性
    data = randn(1,100) * 2 + 10;    % μ=10, σ=2
    [m,ci] = mean_ci(data);
    ok = abs(m-10) < 0.5 && ci < 0.5;  % 期望值
end

function [mean_val, ci_val] = mean_ci(data)
    % 计算平均值和95%置信区间
    mean_val = mean(data);
    std_val = std(data);
    n = length(data);

    % 95%置信区间 (t分布)
    if n > 1
        t_val = tinv(0.975, n-1); % 95%置信区间的t值
        ci_val = t_val * std_val / sqrt(n);
    else
        ci_val = 0;
    end
end
