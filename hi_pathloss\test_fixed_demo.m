% 测试修复后的demo功能

close all;
clear;
clc;

fprintf('=== 测试修复后的Demo功能 ===\n');

try
    % 运行修复版本
    demo_aoi_expected_results_fixed();
    
    fprintf('\n✓ 修复版本运行成功！\n');
    
    % 检查生成的文件
    files_to_check = {
        'expected_aoi_pareto_comparison_fixed.png', 'Pareto前沿图';
        'expected_aoi_scenario_comparison_fixed.png', '场景对比图';
        'expected_aoi_pareto_comparison_fixed.fig', 'Pareto前沿图(FIG)';
        'expected_aoi_scenario_comparison_fixed.fig', '场景对比图(FIG)'
    };
    
    fprintf('\n生成的文件检查:\n');
    for i = 1:size(files_to_check, 1)
        filename = files_to_check{i, 1};
        description = files_to_check{i, 2};
        
        if exist(filename, 'file')
            fprintf('✓ %s - %s\n', filename, description);
        else
            fprintf('✗ %s - %s (未生成)\n', filename, description);
        end
    end
    
    fprintf('\n=== 测试完成 ===\n');
    fprintf('如果看到上述文件都已生成，说明修复成功！\n');
    fprintf('现在可以安全运行原版本: demo_aoi_expected_results\n');
    
catch ME
    fprintf('❌ 测试失败:\n');
    fprintf('错误: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
    
    fprintf('\n请尝试运行更简单的测试:\n');
    fprintf('quick_demo_test\n');
end
