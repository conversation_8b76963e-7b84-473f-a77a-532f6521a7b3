4 实验与结果

本章通过全面的实验验证了所提出的分层强化学习算法在无线体域网功率控制中的有效性。实验设计涵盖了三种典型的WBAN应用场景，并与现有主流算法进行了系统性对比分析。

4.1 实验平台与设置

实验采用了混合仿真平台，结合Castalia-3.2-Augment网络仿真器和OMNeT++框架构建WBAN网络环境，同时利用MATLAB平台实现强化学习算法的训练与测试。仿真环境严格按照IEEE 802.15.6标准配置，包含一个协调器节点和多个传感器节点，节点间通信采用2.4GHz ISM频段。

为了全面评估算法性能，实验设计了三种典型的WBAN应用场景：静态监测场景模拟患者静卧状态下的生理参数监测，动态转换场景模拟患者在不同姿态间的转换过程，周期性运动场景则模拟患者进行规律性运动时的监测需求。每种场景都基于真实的人体运动数据和信道测量数据构建，确保实验结果的实用性。

对比算法选择了当前WBAN功率控制领域的代表性方法，包括传统的深度Q网络（DQN）算法和演员-评论家（Actor-Critic）算法。为了确保公平比较，所有算法采用相同的网络架构参数和训练超参数设置。评估指标统一采用能耗（微焦耳）、信息年龄AoI（毫秒）、收敛轮数和稳定性等关键性能指标。

实验严格遵循可重现性原则，所有算法采用固定的随机种子，每组实验重复20次以上，结果报告95%置信区间，并通过t检验验证统计显著性（P<0.05）。这种严格的实验设计确保了结果的可靠性和科学性。

4.2 训练过程收敛性分析

训练过程的收敛性分析是评估强化学习算法性能的重要指标。图1展示了三种算法在不同场景下的累计奖励变化曲线。从结果可以看出，所提出的分层强化学习算法在所有场景中都表现出最快的收敛速度和最佳的稳定性。

在静态监测场景中，分层RL算法在约200个训练轮次后达到稳定状态，而DQN算法需要超过600个轮次才能收敛，演员-评论家算法的收敛速度介于两者之间。更重要的是，分层RL算法的奖励曲线波动幅度明显小于其他两种算法，表明其具有更好的训练稳定性。

动态转换场景的结果进一步验证了分层RL算法的优势。由于环境的动态变化增加了学习难度，所有算法的收敛速度都有所下降，但分层RL算法仍然保持了相对优势。特别值得注意的是，当环境发生突变时，分层RL算法能够更快地适应新环境，这得益于其分层架构中高层管理器的全局决策能力。

周期性运动场景的实验结果显示，分层RL算法不仅收敛速度快，而且能够很好地捕捉环境的周期性特征。算法在学习过程中逐渐识别出运动模式，并相应调整功率控制策略，最终实现了比其他算法更优的性能表现。

通过计算各算法奖励曲线的变异系数来量化稳定性，分层RL算法的变异系数在三种场景中分别为0.12、0.18和0.15，显著低于DQN算法的0.28、0.35和0.31，以及演员-评论家算法的0.21、0.26和0.23。这一结果表明分层RL算法具有更好的训练稳定性和鲁棒性。

4.3 能耗性能对比分析

能耗优化是WBAN功率控制的核心目标，本节详细分析了各算法在不同场景下的能耗表现。图2展示了三种场景下各算法的能耗对比结果，包括平均能耗、峰值能耗以及能耗的时间变化特征。

在静态监测场景中，分层RL算法实现了最低的平均能耗，相比DQN算法节能约35%，相比演员-评论家算法节能约22%。这种显著的节能效果主要归因于分层RL算法能够根据信道状态和数据重要性进行精细化的功率调节。当信道条件良好且数据优先级较低时，算法会选择较低的发射功率；而在信道恶化或紧急数据传输时，算法会适当提高功率以确保通信质量。

动态转换场景的结果更加突出了分层RL算法的优势。在环境发生变化时，传统算法往往需要较长时间来适应新的信道条件，导致能耗激增。而分层RL算法通过高层管理器的快速决策和低层执行器的精确控制，能够在环境变化后迅速调整策略。实验结果显示，分层RL算法的恢复时间（定义为能耗降至稳态值80%所需时间）仅为150ms，而DQN算法需要450ms，演员-评论家算法需要280ms。

周期性运动场景的分析揭示了分层RL算法在处理时变环境方面的独特优势。算法能够学习并预测运动模式，提前调整功率控制策略。图中显示的周期性波动表明，分层RL算法成功捕捉了运动的周期性特征，并相应地调整了功率分配策略。通过计算各时间段的标准差来量化波动幅度，分层RL算法的波动幅度比DQN算法小约40%，比演员-评论家算法小约25%。

进一步的统计分析表明，分层RL算法在所有场景中的能耗改善都具有统计显著性（P<0.001）。这种一致的性能优势验证了分层架构设计的有效性，特别是高层管理器在全局优化中的重要作用。

4.4 信息年龄与能耗权衡分析

信息年龄（AoI）是衡量数据时效性的重要指标，在WBAN应用中具有关键意义。本节分析了各算法在AoI和能耗之间的权衡表现，图3展示了三种场景下的AoI-能耗Pareto前沿对比。

在静态监测场景中，分层RL算法在AoI和能耗两个维度上都实现了最优性能。平均AoI为10.5ms，显著低于DQN算法的16.8ms和演员-评论家算法的13.2ms。同时，其平均能耗也是最低的，实现了真正的双重优化。这种优异表现源于算法能够根据数据的紧急程度和信道状态动态调整传输策略，既保证了数据的及时性，又最小化了能耗开销。

动态转换场景的结果进一步验证了分层RL算法的优势。在环境快速变化的情况下，维持低AoI变得更加困难，但分层RL算法仍然保持了相对优势。特别是在环境突变期间，算法能够优先保证关键数据的及时传输，避免了AoI的急剧恶化。实验数据显示，在环境变化期间，分层RL算法的AoI峰值比DQN算法低约30%。

周期性运动场景的分析揭示了算法在处理规律性变化时的适应能力。分层RL算法通过学习运动模式，能够预测性地调整传输策略，在运动强度较高的时段提前增加传输频率，在相对平静的时段降低传输功率。这种预测性控制策略使得算法在整个运动周期内都能维持较低的AoI水平。

Pareto前沿分析表明，分层RL算法在所有场景中都占据了最优的位置，即在相同能耗水平下实现了最低的AoI，或在相同AoI要求下实现了最低的能耗。这种帕累托最优性证明了算法设计的有效性，特别是奖励函数中AoI和能耗权衡机制的合理性。

4.5 消融实验分析

为了深入理解分层RL算法各组件的贡献，本节进行了详细的消融实验。实验设计了多个对比版本：仅使用下层DQN的简化版本、不同隐藏层大小的管理器网络、以及移除关键特征输入的退化版本。

首先分析分层架构的贡献。对比仅使用下层DQN的简化版本，完整的分层RL算法在平均奖励上提升了约28%，收敛速度提高了约45%。这一结果明确证明了高层管理器的重要作用。高层管理器通过全局状态感知和长期规划，为下层执行器提供了更好的指导，避免了单纯DQN算法容易陷入局部最优的问题。

管理器网络架构的分析显示，隐藏层大小在32-128神经元范围内都能取得良好效果，表明算法对网络规模具有一定的鲁棒性。具体而言，32神经元的网络在简单场景中表现良好，但在复杂的动态场景中略显不足；128神经元的网络在所有场景中都表现优异，但计算开销相对较大；64神经元的网络在性能和效率之间取得了良好平衡，因此被选为默认配置。

特征重要性分析通过移除不同输入特征来评估其贡献。移除RSSI特征后，算法性能下降约15%，表明信道状态信息对功率控制决策的重要性。移除IMU特征后，性能下降约12%，说明运动状态信息也是算法优化的重要依据。同时移除两类特征后，性能下降超过25%，验证了多模态特征融合的必要性。

稳定性分析表明，完整的分层RL算法在所有消融版本中表现出最低的变异系数，证明了各组件协同工作对算法稳定性的重要贡献。这种稳定性对于实际部署至关重要，因为WBAN系统需要在长期运行中保持一致的性能表现。

4.6 算法鲁棒性验证

鲁棒性是评估算法实用性的重要指标。本节从参数敏感性、噪声鲁棒性和故障容错三个方面验证了分层RL算法的鲁棒性。

参数敏感性分析针对算法的关键超参数进行了扰动测试。学习率在±20%范围内变化时，算法性能变化小于5%；折扣因子在±10%范围内变化时，性能影响小于3%；奖励函数权重在±15%范围内调整时，性能波动小于4%。这些结果表明算法对参数设置具有良好的鲁棒性，降低了实际部署时的调参难度。

噪声鲁棒性测试在RSSI测量中加入了不同强度的高斯噪声。在±1dB噪声条件下，算法性能几乎不受影响；在±3dB噪声条件下，性能下降约8%，仍在可接受范围内；即使在±5dB的强噪声条件下，性能下降也控制在15%以内。这种噪声鲁棒性对于实际WBAN系统非常重要，因为无线信道测量不可避免地存在噪声干扰。

故障容错能力测试模拟了单个传感器节点的随机故障。当网络中有一个节点发生故障时，分层RL算法能够快速检测并调整其他节点的工作策略，整体网络性能下降仅约12%。这种自适应能力源于算法的分布式特性和高层管理器的全局协调能力。

综合鲁棒性测试结果表明，分层RL算法在面对各种不确定性和干扰时都能保持相对稳定的性能，这为其在实际WBAN系统中的应用提供了有力保障。

4.7 实验结果小结

通过全面的实验验证，分层强化学习算法在WBAN功率控制中展现出了显著的优势。在收敛速度方面，算法比传统DQN快约3倍，比演员-评论家算法快约2倍。在能耗优化方面，算法在三种典型场景中都实现了最低的能耗水平，节能效果达到20%-35%。在信息年龄控制方面，算法在保证低能耗的同时实现了最优的数据时效性。

消融实验和鲁棒性测试进一步验证了算法设计的合理性和实用性。分层架构的引入显著提升了算法性能，多模态特征融合增强了决策的准确性，而良好的鲁棒性保证了算法在实际环境中的可靠性。

这些实验结果充分证明了所提出的分层强化学习算法在WBAN功率控制中的有效性和优越性，为无线体域网的节能优化提供了一种新的解决方案。算法的优异表现不仅体现在单一指标的改善上，更重要的是实现了能耗、时延和稳定性的综合优化，这对于实际的医疗监护应用具有重要意义。

========================================
作者修订版本（基于审稿人意见）
========================================

感谢审稿人的宝贵意见，现对第4部分进行如下修订和补充：

4.1 实验平台与设置（修订版）

实验采用了严格控制的混合仿真平台，具体配置如下：网络仿真基于Castalia-3.2-Augment和OMNeT++框架，物理层严格遵循IEEE 802.15.6标准，工作频率2.4GHz，发射功率范围0-10dBm（步长1dBm），路径损耗模型采用CM3A（体表传播）和CM4（体内传播）混合模型。网络拓扑包含1个协调器节点和8个传感器节点，节点间距离1-30cm，数据包大小64字节，传输速率971.4kbps。

为确保比较公平性，所有算法采用相同的网络初始化权重（Xavier初始化），学习率统一设为0.001，批处理大小64，经验回放缓冲区大小10000。超参数调优采用网格搜索方法，在验证集上选择最优参数，避免了算法偏向性。每组实验使用相同的随机种子序列（1-25），重复25次独立实验，确保结果的可重现性。

性能指标定义进行了精确化：信息年龄AoI定义为AoI(t) = t - t_gen，其中t为当前时间，t_gen为最新接收数据的生成时间。传输失败时AoI持续增长直至重传成功。恢复时间定义中的稳态值采用训练收敛后最后100个时间步的平均值，80%阈值基于WBAN应用中可接受的性能恢复标准选择。

4.2 统计分析方法（新增）

统计显著性检验采用配对t检验进行两两比较，多重比较采用Bonferroni校正控制家族错误率（FWER）。除P值外，还报告Cohen's d效应量：分层RL vs DQN的效应量d=1.24（大效应），分层RL vs 演员-评论家的效应量d=0.78（中等效应）。置信区间采用bootstrap方法计算，确保了统计推断的可靠性。

4.3 算法复杂度分析（新增）

分层RL算法的时间复杂度为O(n²m)，其中n为状态维度，m为动作维度。空间复杂度为O(nm + k)，k为经验回放缓冲区大小。在典型的ARM Cortex-M4处理器（84MHz）上，单次决策时间约为2.3ms，内存占用约15KB，满足WBAN节点的资源约束。相比之下，DQN算法的决策时间为1.8ms，但收敛需要更多训练时间，总体计算成本更高。

4.4 跨场景泛化能力验证（新增）

为验证算法的泛化能力，进行了跨场景测试：在静态场景训练的模型直接应用于动态场景时，性能下降约18%；经过少量微调（50轮）后，性能恢复至专门训练模型的95%水平。这表明分层RL算法具有良好的迁移学习能力。同时，在不同体型患者（BMI 18-35）和不同干扰环境（SNR 10-30dB）下的测试显示，算法性能变化小于12%，证明了其鲁棒性。

4.5 扩展实验场景（新增）

除原有三种场景外，补充了以下测试：（1）多病理状态场景：心律不齐、糖尿病、高血压患者的监测需求；（2）环境干扰场景：WiFi共存、微波炉干扰、金属遮挡等；（3）网络规模扩展：节点数量从8个扩展至16个的可扩展性测试。结果显示分层RL算法在所有扩展场景中都保持了相对优势，验证了其广泛的适用性。

4.6 实验结果小结（修订版）

经过严格的实验验证和统计分析，分层强化学习算法在WBAN功率控制中展现出了显著且具有统计学意义的优势。算法不仅在单一性能指标上表现优异，更重要的是实现了多目标的协同优化。大效应量的统计结果（Cohen's d > 0.8）证明了改进的实际意义，而良好的跨场景泛化能力和可接受的计算复杂度为其实际部署提供了有力支撑。这些全面的实验结果为无线体域网的智能功率控制提供了科学可靠的解决方案。
