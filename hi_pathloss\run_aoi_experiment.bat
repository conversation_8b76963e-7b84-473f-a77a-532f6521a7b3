@echo off
echo ========================================
echo AoI对比实验运行脚本
echo ========================================
echo.

echo 步骤1: 运行预期结果演示
echo 正在启动MATLAB...
matlab -batch "cd('hi_pathloss'); demo_aoi_expected_results; exit"

echo.
echo 步骤2: 运行快速测试
echo 正在运行快速测试...
matlab -batch "cd('hi_pathloss'); test_aoi_experiment; exit"

echo.
echo 步骤3: 运行完整实验 (可选)
echo 如需运行完整实验，请手动执行以下命令:
echo matlab -batch "cd('hi_pathloss'); aoi_comparison_experiment; exit"

echo.
echo ========================================
echo 实验完成！请查看生成的图表和结果文件
echo ========================================
pause
