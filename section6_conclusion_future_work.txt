6 结论与未来工作

本研究针对无线体域网功率控制中的能耗优化问题，提出了一种基于分层强化学习的智能功率控制算法。通过构建双层决策架构，算法实现了全局策略规划与局部精确控制的有机结合，在保证通信质量的前提下显著降低了系统能耗。

研究的主要贡献体现在三个方面：首先，设计了适用于WBAN特殊环境的分层强化学习框架，高层管理器负责长期策略规划，低层执行器实现精确功率调节，有效解决了传统单层算法在复杂动态环境中的局限性。其次，提出了融合信道状态、运动模式和数据优先级的多维状态表示方法，使算法能够根据实时环境变化做出智能决策。最后，建立了能耗与信息年龄的联合优化目标函数，实现了系统性能的多维度协同提升。

实验结果表明，所提算法在三种典型WBAN应用场景中均表现出显著优势。相比传统DQN算法，节能效果达到20%-35%，收敛速度提升约3倍；相比演员-评论家算法，在保持相似收敛速度的同时实现了更低的能耗和信息年龄。消融实验验证了分层架构和多模态特征融合的有效性，鲁棒性测试证明了算法在噪声干扰和参数扰动下的稳定性。

尽管取得了显著进展，本研究仍存在一些局限性和改进空间。未来工作将从以下几个方向展开：首先，扩展算法的适用范围，研究多信道和多跳网络环境下的功率控制策略，以适应更复杂的WBAN拓扑结构。其次，探索与其他先进强化学习算法的融合，如基于注意力机制的深度强化学习和元学习方法，进一步提升算法的学习效率和泛化能力。此外，将研究重点转向实际硬件平台的部署优化，包括算法的轻量化设计、实时性保证和功耗评估，为算法的产业化应用奠定基础。最后，考虑将算法扩展至更广泛的物联网场景，如智能家居、工业监测等领域，探索其在不同应用环境中的适应性和有效性。

总体而言，本研究为无线体域网的智能功率控制提供了一种有效的解决方案，在理论创新和实际应用方面都具有重要价值。随着相关技术的不断发展和完善，分层强化学习方法有望在更广泛的无线通信领域发挥重要作用，为构建高效、智能的无线网络系统贡献力量。

========================================
作者修订版本（基于审稿人意见）
========================================

6 结论与未来工作（修订版）

本研究提出的分层强化学习算法在WBAN功率控制中实现了显著的性能提升：相比DQN算法节能20%-35%且收敛速度提升3倍，相比演员-评论家算法在相似收敛速度下实现更优的能耗-AoI权衡。核心技术贡献包括：（1）双层决策架构解决了单层RL在时变环境中的探索-利用困境，理论上保证了策略的长期最优性；（2）多模态状态融合机制将决策准确率提升15%；（3）联合优化目标函数实现了帕累托最优解。

然而，当前方法存在明确的局限性：算法适用于节点数≤16的小规模网络，在大规模或高动态场景下收敛性能下降；计算复杂度O(n²m)限制了在超低功耗设备上的部署；算法假设信道状态可准确估计，在强干扰环境下性能可能显著退化。此外，实验主要基于仿真环境，缺乏真实硬件平台的验证。

基于技术成熟度和应用需求，未来工作按优先级排序为：（1）短期目标：算法轻量化设计和硬件平台验证，预计6个月内完成原型系统；（2）中期目标：扩展至多信道环境和网络规模优化，技术难度中等但应用价值高；（3）长期目标：融合元学习和注意力机制，属于前沿探索但风险较高。

在实际应用方面，算法需要克服医疗设备认证、数据隐私保护和跨平台兼容性等挑战。建议与医疗设备厂商合作，建立符合FDA/CE标准的验证流程，并开发标准化的API接口以促进产业化应用。

总体而言，本研究在理论创新和实用性之间取得了良好平衡，为WBAN智能功率控制提供了可行的技术路径，具备向产业化转化的潜力。
