% 2. 再运行一次确定可复现性

clear; close all; clc;

scenario = struct('name','静态测试','file','13_04.bvh','type','static');

rng('default');                    % 重置随机种子
res1 = run_single_scenario_experiment(scenario);
rng('default');                    % 再次同种子执行
res2 = run_single_scenario_experiment(scenario);

% 检查结果是否相等
assert(isequal(res1.final_avg_reward, ...
              res2.final_avg_reward), ...
       '随机种子复现失败');

fprintf('如 assert 无报错，说明随机种子起作用。\n');

function training_results = run_single_scenario_experiment(scenario)
    % 简化的场景实验函数，用于测试多随机种子和置信区间
    
    % 使用固定的scenario结构
    if nargin < 1
        scenario = struct('name','静态测试','file','13_04.bvh','type','static');
    end
    
    fprintf('开始场景实验: %s\n', scenario.name);
    
    % 多随机种子设置
    num_runs = 5;
    seed_list = 1:num_runs;
    
    % 预分配结果数组
    rewards_arr = zeros(num_runs,1);
    energy_arr  = zeros(num_runs,1);
    pdr_arr     = zeros(num_runs,1);
    delay_arr   = zeros(num_runs,1);
    
    fprintf('使用 %d 个随机种子独立训练\n', num_runs);
    
    % 模拟多次训练结果
    for run_idx = 1:num_runs
        rng(seed_list(run_idx), 'twister');
        
        % 模拟训练结果（添加随机性）
        base_reward = 50000 + randn() * 5000;
        base_energy = 0.8 + randn() * 0.1;
        base_pdr = 0.95 + randn() * 0.02;
        base_delay = 0.05 + randn() * 0.01;
        
        rewards_arr(run_idx) = base_reward;
        energy_arr(run_idx) = base_energy;
        pdr_arr(run_idx) = base_pdr;
        delay_arr(run_idx) = base_delay;
        
        fprintf('多次训练统计: Reward=%.1f, Energy=%.3f\n', base_reward, base_energy);
    end
    
    % 计算平均值和95%置信区间
    [avg_reward, ci_reward] = mean_ci(rewards_arr);
    [avg_energy, ci_energy] = mean_ci(energy_arr);
    [avg_pdr, ci_pdr] = mean_ci(pdr_arr);
    [avg_delay, ci_delay] = mean_ci(delay_arr);
    
    % 构建训练结果结构体
    training_results = struct();
    training_results.final_avg_reward = avg_reward;
    training_results.final_avg_energy = avg_energy;
    training_results.final_avg_pdr = avg_pdr;
    training_results.final_avg_delay = avg_delay;
    training_results.ci_reward = ci_reward;
    training_results.ci_energy = ci_energy;
    training_results.ci_pdr = ci_pdr;
    training_results.ci_delay = ci_delay;
    training_results.num_runs = num_runs;
end

function [mean_val, ci_val] = mean_ci(data)
    % 计算平均值和95%置信区间
    mean_val = mean(data);
    std_val = std(data);
    n = length(data);

    % 95%置信区间 (t分布)
    if n > 1
        t_val = tinv(0.975, n-1); % 95%置信区间的t值
        ci_val = t_val * std_val / sqrt(n);
    else
        ci_val = 0;
    end
end
