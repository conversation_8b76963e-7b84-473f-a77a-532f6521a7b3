% run_aoi_comparison.m
% 对比 Hierarchical RL, DQN, Actor-Critic 三种算法在三类场景下的 AoI-能耗 Pareto
% 不修改原始文件，独立脚本可直接运行。

clear; clc; close all;
addpath(genpath('.'));

% ---------- 场景与数据 ----------
scenarios = {
    struct('name','静态监测','file','../13_04_pl.txt','type','static')
    struct('name','动态转换','file','../13_01_pl.txt','type','dynamic')
    struct('name','周期性运动','file','../35_01_pl.txt','type','periodic')
};

% 统一训练参数（可适当缩短以加快演示）
params = struct(...
    'num_episodes', 40, ...
    'max_steps_per_episode', 200, ...
    'batch_size', 32, ...
    'update_frequency', 10, ...
    'learning_rate', 0.001, ...
    'gamma', 0.95, ...
    'epsilon', 1.0, ...
    'epsilon_decay', 0.995, ...
    'epsilon_min', 0.05 ...
);

% 结果容器
results = struct();

for s = 1:numel(scenarios)
    sc = scenarios{s};
    fprintf('\n=== 场景: %s ===\n', sc.name);
    % 载入测量路径损耗
    data = readmatrix(sc.file);
    pl = data(:,2);
    env = make_wban_env_from_pl(pl, sc.type, 0.1);

    % ---- 训练三类算法 ----
    % 1) Hierarchical RL
    [agent_hrl, res_hrl] = train_hierarchical_rl(env, struct('name',sc.name,'type',sc.type));
    metrics_hrl = evaluate_aoi_metrics(agent_hrl, make_wban_env_from_pl(pl, sc.type, 0.1));

    % 2) DQN
    [agent_dqn, res_dqn] = train_dqn(env, params);
    metrics_dqn = evaluate_aoi_metrics(agent_dqn, make_wban_env_from_pl(pl, sc.type, 0.1), 'dqn');

    % 3) Actor-Critic (简化版本)
    ac_params = params; ac_params.learning_rate = 0.0008;
    [agent_ac, res_ac] = train_actor_critic(env, ac_params);
    metrics_ac = evaluate_aoi_metrics(agent_ac, make_wban_env_from_pl(pl, sc.type, 0.1), 'ac');

    % 保存
    results(s).name = sc.name;
    results(s).hrl = metrics_hrl; results(s).dqn = metrics_dqn;
    results(s).ac = metrics_ac;

    % 绘制 AoI-能耗 Pareto（单场景）
    figure('Name', sprintf('AoI-Pareto-%s', sc.name));
    plot_pareto_single(results(s));
end

% 绘制跨场景综合图
figure('Name','AoI-Pareto-三场景对比');
plot_pareto_all(results);

% ---------- 辅助函数 ----------
function m = evaluate_aoi_metrics(agent, env, mode)
    if nargin < 3, mode = 'hrl'; end
    % rollout 一次获取 AoI/能耗
    aoi_vals = [];
    energy_vals = [];
    % 从环境推断时间步长
    if isfield(env,'time_vector') && numel(env.time_vector) >= 2
        time_step = env.time_vector(2) - env.time_vector(1);
    else
        time_step = 0.1; % fallback
    end
    last_success_time = 0; % 上次成功到达时间
    state = env.reset(); total_energy = 0;
    for k = 1:env.max_steps
        switch mode
            case 'hrl'
                action = hrl_select_action(agent, state);
            case 'dqn'
                q = dqn_forward_q(agent.q_weights, state); [~,action] = max(q);
            case 'ac'
                action = actor_select(agent, state);
            otherwise
                action = 4;
        end
        [next_state, ~, done, info] = env.step(action);
        total_energy = total_energy + info.energy;
        % AoI 采样：先根据旧的 last_success_time 计算，再决定是否更新
        t = k * time_step;
        aoi_now = t - last_success_time;  % 秒
        if info.pdr > 0.8
            last_success_time = t;        % 成功到达，刷新基准
        end
        aoi_vals(end+1) = aoi_now; %#ok<AGROW>
        energy_vals(end+1) = info.energy; %#ok<AGROW>
        state = next_state; if done, break; end
    end
    m = struct();
    m.mean_aoi = mean(aoi_vals)*1000;  % 转换为 ms
    m.mean_energy = mean(energy_vals);
    m.aoi_series = aoi_vals; m.energy_series = energy_vals;
end

function plot_pareto_single(r)
    hold on; grid on;
    scatter(r.hrl.mean_energy, r.hrl.mean_aoi, 80, 'filled', 'o');
    scatter(r.dqn.mean_energy, r.dqn.mean_aoi, 80, 's');
    scatter(r.ac.mean_energy,  r.ac.mean_aoi,  80, '^');
    xlabel('平均能耗 (mJ)'); ylabel('平均AoI (ms)');
    legend('Hierarchical RL','DQN','Actor-Critic','Location','best');
    title('AoI-能耗 Pareto (单场景)');
end

function plot_pareto_all(results)
    hold on; grid on;
    mk = {'o','s','^'}; clr = lines(3);
    for s=1:numel(results)
        r = results(s);
        scatter(r.hrl.mean_energy, r.hrl.mean_aoi, 90, clr(s,:), mk{1}, 'filled');
        scatter(r.dqn.mean_energy, r.dqn.mean_aoi, 90, clr(s,:), mk{2});
        scatter(r.ac.mean_energy,  r.ac.mean_aoi,  90, clr(s,:), mk{3});
    end
    xlabel('平均能耗 (mJ)'); ylabel('平均AoI (ms)');
    legend({'HRL-静态','DQN-静态','AC-静态', ...
            'HRL-动态','DQN-动态','AC-动态', ...
            'HRL-周期','DQN-周期','AC-周期'}, 'Location','bestoutside');
    title('AoI-能耗 Pareto（静态/动态/周期）');
end

% ========= 简化 Actor-Critic 实现 =========
function [agent, results] = train_actor_critic(env, params)
    % Very lightweight actor-critic with policy logits + value head
    agent = struct();
    agent.state_dim = env.state_dim; agent.action_dim = env.action_dim;
    H = 64;
    agent.W1 = randn(H,agent.state_dim)*sqrt(2/agent.state_dim);
    agent.b1 = zeros(H,1);
    agent.Wp = randn(agent.action_dim,H)*sqrt(2/H); agent.bp=zeros(agent.action_dim,1);
    agent.Wv = randn(1,H)*sqrt(2/H); agent.bv=0;
    agent.lr = params.learning_rate; agent.gamma = params.gamma;
    agent.eps = 1.0; agent.eps_decay = params.epsilon_decay; agent.eps_min=params.epsilon_min;

    results.episode_rewards = zeros(params.num_episodes,1);

    for ep=1:params.num_episodes
        s = env.reset(); ep_r=0;
        for t=1:params.max_steps_per_episode
            if rand()<agent.eps
                a = randi(env.action_dim);
            else
                a = actor_select(agent, s);
            end
            [s2, r, done, ~] = env.step(a);
            % compute td-target
            v  = critic_forward(agent, s);
            v2 = critic_forward(agent, s2);
            td = r + agent.gamma*(1-done)*v2 - v;
            % grads
            [logp, pi] = actor_forward(agent, s);
            grad_logits = - (onehot(a,env.action_dim) - pi) * td; % policy gradient
            % backprop hidden
            z1 = agent.W1*s + agent.b1; a1 = max(0,z1);
            % actor head grads
            gWp = grad_logits * a1'; gbp = grad_logits;
            % critic head grads
            gv_out = -td; gWv = gv_out * a1'; gbv = gv_out;
            % hidden layer grads (sum heads)
            ga1 = agent.Wp' * grad_logits + agent.Wv' * gv_out;
            gz1 = ga1 .* (z1>0);
            gW1 = gz1 * s'; gb1 = gz1;
            % update
            agent.Wp = agent.Wp - agent.lr * gWp; agent.bp = agent.bp - agent.lr * gbp;
            agent.Wv = agent.Wv - agent.lr * gWv; agent.bv = agent.bv - agent.lr * gbv;
            agent.W1 = agent.W1 - agent.lr * gW1; agent.b1 = agent.b1 - agent.lr * gb1;

            s = s2; ep_r = ep_r + r; if done, break; end
        end
        agent.eps = max(agent.eps_min, agent.eps * agent.eps_decay);
        results.episode_rewards(ep)=ep_r;
    end
    results.final_avg_reward = mean(results.episode_rewards(max(1,end-9):end));
end

function a = actor_select(agent, s)
    [~, pi] = actor_forward(agent, s); [~,a]=max(pi);
end

function v = critic_forward(agent, s)
    z1 = agent.W1*s + agent.b1; a1 = max(0,z1);
    v = agent.Wv*a1 + agent.bv;
end

function [logits, pi] = actor_forward(agent, s)
    z1 = agent.W1*s + agent.b1; a1 = max(0,z1);
    logits = agent.Wp*a1 + agent.bp; pi = softmax_vec(logits);
end

function v = onehot(k, n)
    v = zeros(n,1); v(k)=1;
end

function y = softmax_vec(x)
    ex = exp(x - max(x)); y = ex/sum(ex);
end

% ------- 选择/前向辅助 (避免依赖其他文件的局部函数) -------
function action = hrl_select_action(agent, state)
    % 上层
    z1 = agent.upper_weights.W1*state + agent.upper_weights.b1; a1 = max(0,z1);
    z2 = agent.upper_weights.W2*a1   + agent.upper_weights.b2; a2 = max(0,z2);
    upper_out = agent.upper_weights.W3*a2 + agent.upper_weights.b3;
    pw = softmax_vec(upper_out);
    % 下层
    input_low = [state; pw];
    z1l = agent.lower_weights.W1*input_low + agent.lower_weights.b1; a1l = max(0,z1l);
    z2l = agent.lower_weights.W2*a1l      + agent.lower_weights.b2; a2l = max(0,z2l);
    q    = agent.lower_weights.W3*a2l + agent.lower_weights.b3;
    [~,action] = max(q);
end

function q = dqn_forward_q(W, s)
    a1 = max(0, W.W1*s + W.b1);
    q  = W.W2*a1 + W.b2;
end
