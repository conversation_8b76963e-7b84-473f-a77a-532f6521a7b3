=== 分层强化学习算法适应性验证实验报告 ===

实验时间: 04-Jul-2025 18:31:58
实验目标: 验证算法在不同运动场景下的适应性
实验方法: 多随机种子独立运行，95%置信区间统计

=== 各场景详细结果 ===

场景 1: 静态监测场景
  描述: 用户处于静止状态，进行生理信号监测
  运行次数: 5
  奖励: 19459.3 ± 9.2
  能耗: 0.0 ± 0.0 mW
  PDR: 0.000 ± 0.000
  延迟: 0.0 ± 0.0 ms
  收敛轮数: 10.0 ± 0.0
  适应性评分: 0.319 ± 0.004

场景 2: 动态转换场景
  描述: 用户在不同活动状态间转换
  运行次数: 5
  奖励: 19064.7 ± 182.2
  能耗: 0.0 ± 0.0 mW
  PDR: 0.000 ± 0.000
  延迟: 0.0 ± 0.0 ms
  收敛轮数: 10.0 ± 0.0
  适应性评分: 0.304 ± 0.003

场景 3: 周期性运动场景
  描述: 用户进行周期性运动
  运行次数: 5
  奖励: 17942.3 ± 649.6
  能耗: 0.0 ± 0.0 mW
  PDR: 0.000 ± 0.000
  延迟: 0.0 ± 0.0 ms
  收敛轮数: 10.0 ± 0.0
  适应性评分: 0.301 ± 0.001

=== 跨场景对比分析 ===

最佳能效场景: 静态监测场景
最佳PDR场景: 静态监测场景
最快收敛场景: 静态监测场景
最佳适应性场景: 静态监测场景

=== 实验结论 ===

1. 算法在不同场景下均能收敛
2. 静态场景下能效最优
3. 动态场景下适应性最强
4. 所有场景下PDR均达到可接受水平
