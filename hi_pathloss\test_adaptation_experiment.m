% 简化的适应性实验测试
function test_adaptation_experiment()
    fprintf('=== 测试适应性实验模块 ===\n');
    
    % 设置随机种子
    rng(42);
    
    % 创建测试场景
    scenarios = create_test_scenarios();
    
    % 运行简化实验
    results = run_simplified_experiment(scenarios);
    
    % 分析结果
    analyze_experiment_results(results, scenarios);
    
    fprintf('适应性实验测试完成!\n');
end

function scenarios = create_test_scenarios()
    % 创建测试场景
    scenarios = cell(3, 1);
    
    scenarios{1} = struct('name', '静态监测', 'type', 'static', ...
                         'duration', 100, 'mobility', 'low');
    scenarios{2} = struct('name', '动态转换', 'type', 'dynamic', ...
                         'duration', 100, 'mobility', 'medium');
    scenarios{3} = struct('name', '高强度运动', 'type', 'exercise', ...
                         'duration', 100, 'mobility', 'high');
    
    fprintf('创建了 %d 个测试场景\n', length(scenarios));
end

function results = run_simplified_experiment(scenarios)
    % 运行简化实验
    fprintf('开始运行简化实验...\n');
    
    num_runs = 3; % 简化为3次运行
    results = cell(length(scenarios), 1);
    
    for s = 1:length(scenarios)
        scenario = scenarios{s};
        fprintf('\n--- 场景 %d: %s ---\n', s, scenario.name);
        
        % 多次独立运行
        run_results = cell(num_runs, 1);
        
        for run = 1:num_runs
            fprintf('运行 %d/%d...\n', run, num_runs);
            
            % 设置不同的随机种子
            rng(42 + run * 10 + s);
            
            % 运行单次实验
            run_results{run} = run_single_experiment(scenario);
        end
        
        % 汇总结果
        results{s} = aggregate_run_results(run_results, scenario);
    end
    
    fprintf('实验运行完成\n');
end

function result = run_single_experiment(scenario)
    % 运行单次实验
    
    % 创建环境
    env = create_simple_environment(scenario);
    
    % 创建智能体
    agent = create_simple_agent(env);
    
    % 训练
    training_result = run_simple_training(agent, env, scenario);
    
    % 评估
    eval_result = run_simple_evaluation(agent, env);
    
    result = struct();
    result.training = training_result;
    result.evaluation = eval_result;
    result.scenario = scenario;
end

function env = create_simple_environment(scenario)
    % 创建简化环境
    env = struct();
    env.state_dim = 8;
    env.action_dim = 6;
    env.max_steps = scenario.duration;
    env.scenario_type = scenario.type;
    
    % 根据场景类型设置参数
    switch scenario.type
        case 'static'
            env.noise_level = 0.1;
            env.channel_variation = 0.2;
        case 'dynamic'
            env.noise_level = 0.2;
            env.channel_variation = 0.4;
        case 'exercise'
            env.noise_level = 0.3;
            env.channel_variation = 0.6;
    end
    
    env.current_step = 0;
    env.total_energy = 0;
    env.successful_transmissions = 0;
    env.total_transmissions = 0;
end

function agent = create_simple_agent(env)
    % 创建简化智能体
    agent = struct();
    agent.state_dim = env.state_dim;
    agent.action_dim = env.action_dim;
    agent.learning_rate = 0.001;
    agent.epsilon = 1.0;
    agent.epsilon_decay = 0.995;
    agent.epsilon_min = 0.01;
    agent.gamma = 0.9;
    
    % 简化的网络权重
    agent.weights = struct();
    agent.weights.W = randn(env.action_dim, env.state_dim) * 0.1;
    agent.weights.b = zeros(env.action_dim, 1);
    
    agent.memory = [];
    agent.episode_rewards = [];
    agent.episode_losses = [];
end

function training_result = run_simple_training(agent, env, scenario)
    % 运行简化训练
    num_episodes = 20; % 简化为20轮
    episode_rewards = zeros(num_episodes, 1);
    episode_energy = zeros(num_episodes, 1);
    episode_pdr = zeros(num_episodes, 1);
    episode_delay = zeros(num_episodes, 1);
    
    for episode = 1:num_episodes
        state = reset_simple_env(env);
        episode_reward = 0;
        
        for step = 1:env.max_steps
            % 选择动作
            action = select_simple_action(agent, state);
            
            % 环境步进
            [next_state, reward, done, info] = step_simple_env(env, action);
            
            episode_reward = episode_reward + reward;
            state = next_state;
            
            if done
                break;
            end
        end
        
        % 记录结果
        episode_rewards(episode) = episode_reward;
        episode_energy(episode) = env.total_energy;
        episode_pdr(episode) = env.successful_transmissions / max(1, env.total_transmissions);
        episode_delay(episode) = 20 + randn() * 5; % 模拟延迟
        
        % 简单的学习更新
        agent.epsilon = max(agent.epsilon_min, agent.epsilon * agent.epsilon_decay);
        
        if mod(episode, 5) == 0
            fprintf('  Episode %d: Reward=%.1f, Energy=%.1f, PDR=%.3f\n', ...
                    episode, episode_reward, episode_energy(episode), episode_pdr(episode));
        end
    end
    
    training_result = struct();
    training_result.episode_rewards = episode_rewards;
    training_result.episode_energy = episode_energy;
    training_result.episode_pdr = episode_pdr;
    training_result.episode_delay = episode_delay;
    training_result.final_avg_reward = mean(episode_rewards(end-4:end));
    training_result.final_avg_energy = mean(episode_energy(end-4:end));
    training_result.final_avg_pdr = mean(episode_pdr(end-4:end));
    training_result.final_avg_delay = mean(episode_delay(end-4:end));
    training_result.convergence_episode = find_simple_convergence(episode_rewards);
end

function eval_result = run_simple_evaluation(agent, env)
    % 运行简化评估
    num_eval = 5;
    eval_rewards = zeros(num_eval, 1);
    eval_energy = zeros(num_eval, 1);
    
    for i = 1:num_eval
        state = reset_simple_env(env);
        episode_reward = 0;
        
        for step = 1:env.max_steps
            action = select_simple_action(agent, state);
            [next_state, reward, done, ~] = step_simple_env(env, action);
            
            episode_reward = episode_reward + reward;
            state = next_state;
            
            if done
                break;
            end
        end
        
        eval_rewards(i) = episode_reward;
        eval_energy(i) = env.total_energy;
    end
    
    eval_result = struct();
    eval_result.avg_reward = mean(eval_rewards);
    eval_result.avg_energy = mean(eval_energy);
    eval_result.std_reward = std(eval_rewards);
    eval_result.std_energy = std(eval_energy);
end

function state = reset_simple_env(env)
    % 重置简化环境
    env.current_step = 0;
    env.total_energy = 0;
    env.successful_transmissions = 0;
    env.total_transmissions = 0;
    
    % 生成初始状态
    state = randn(env.state_dim, 1) * 0.5;
end

function [next_state, reward, done, info] = step_simple_env(env, action)
    % 环境步进
    env.current_step = env.current_step + 1;
    env.total_transmissions = env.total_transmissions + 1;
    
    % 计算能耗 (基于动作)
    power_levels = [0.1, 0.5, 1.0, 2.0, 4.0, 8.0]; % mW
    energy = power_levels(action);
    env.total_energy = env.total_energy + energy;
    
    % 计算PDR (功率越高PDR越好，但有噪声)
    base_pdr = 0.3 + 0.6 * (action / 6);
    pdr = base_pdr + randn() * env.noise_level;
    pdr = max(0, min(1, pdr));
    
    if rand() < pdr
        env.successful_transmissions = env.successful_transmissions + 1;
    end
    
    % 计算奖励 (PDR奖励 - 能耗惩罚)
    reward = pdr * 100 - energy * 2;
    
    % 生成下一状态
    next_state = randn(env.state_dim, 1) * 0.5;
    
    % 结束条件
    done = env.current_step >= env.max_steps;
    
    info = struct('energy', energy, 'pdr', pdr);
end

function action = select_simple_action(agent, state)
    % 简单动作选择
    if rand() < agent.epsilon
        action = randi(agent.action_dim);
    else
        q_values = agent.weights.W * state + agent.weights.b;
        [~, action] = max(q_values);
    end
end

function convergence_episode = find_simple_convergence(episode_rewards)
    % 简单收敛检测
    if length(episode_rewards) < 10
        convergence_episode = length(episode_rewards);
        return;
    end
    
    % 检查最后10轮的稳定性
    last_rewards = episode_rewards(end-9:end);
    if std(last_rewards) / abs(mean(last_rewards)) < 0.1
        convergence_episode = length(episode_rewards) - 5;
    else
        convergence_episode = length(episode_rewards);
    end
end

function aggregated = aggregate_run_results(run_results, scenario)
    % 汇总多次运行结果
    num_runs = length(run_results);
    
    % 提取关键指标
    final_rewards = zeros(num_runs, 1);
    final_energy = zeros(num_runs, 1);
    final_pdr = zeros(num_runs, 1);
    final_delay = zeros(num_runs, 1);
    convergence_episodes = zeros(num_runs, 1);
    
    for i = 1:num_runs
        tr = run_results{i}.training;
        final_rewards(i) = tr.final_avg_reward;
        final_energy(i) = tr.final_avg_energy;
        final_pdr(i) = tr.final_avg_pdr;
        final_delay(i) = tr.final_avg_delay;
        convergence_episodes(i) = tr.convergence_episode;
    end
    
    % 计算统计量
    aggregated = struct();
    aggregated.scenario = scenario;
    aggregated.num_runs = num_runs;
    
    % 平均值和置信区间
    [aggregated.mean_reward, aggregated.ci_reward] = mean_ci_simple(final_rewards);
    [aggregated.mean_energy, aggregated.ci_energy] = mean_ci_simple(final_energy);
    [aggregated.mean_pdr, aggregated.ci_pdr] = mean_ci_simple(final_pdr);
    [aggregated.mean_delay, aggregated.ci_delay] = mean_ci_simple(final_delay);
    [aggregated.mean_convergence, aggregated.ci_convergence] = mean_ci_simple(convergence_episodes);
    
    aggregated.raw_results = run_results;
end

function [mean_val, ci_val] = mean_ci_simple(data)
    % 计算平均值和95%置信区间
    mean_val = mean(data);
    std_val = std(data);
    n = length(data);
    
    if n > 1
        t_val = 2.0; % 简化的t值 (近似95%置信区间)
        ci_val = t_val * std_val / sqrt(n);
    else
        ci_val = 0;
    end
end

function analyze_experiment_results(results, scenarios)
    % 分析实验结果
    fprintf('\n=== 实验结果分析 ===\n');
    
    for i = 1:length(results)
        result = results{i};
        scenario = scenarios{i};
        
        fprintf('\n场景 %d: %s\n', i, scenario.name);
        fprintf('  奖励: %.1f ± %.1f\n', result.mean_reward, result.ci_reward);
        fprintf('  能耗: %.1f ± %.1f mW\n', result.mean_energy, result.ci_energy);
        fprintf('  PDR: %.3f ± %.3f\n', result.mean_pdr, result.ci_pdr);
        fprintf('  延迟: %.1f ± %.1f ms\n', result.mean_delay, result.ci_delay);
        fprintf('  收敛轮数: %.1f ± %.1f\n', result.mean_convergence, result.ci_convergence);
    end
    
    % 生成对比图
    generate_comparison_plot(results, scenarios);
end

function generate_comparison_plot(results, scenarios)
    % 生成对比图
    figure('Name', '适应性实验结果', 'Position', [100, 100, 1000, 600]);
    
    % 提取数据
    mean_energy = zeros(length(results), 1);
    ci_energy = zeros(length(results), 1);
    mean_pdr = zeros(length(results), 1);
    ci_pdr = zeros(length(results), 1);
    
    for i = 1:length(results)
        mean_energy(i) = results{i}.mean_energy;
        ci_energy(i) = results{i}.ci_energy;
        mean_pdr(i) = results{i}.mean_pdr;
        ci_pdr(i) = results{i}.ci_pdr;
    end
    
    % 能耗对比
    subplot(2, 2, 1);
    errorbar(1:length(results), mean_energy, ci_energy, 'bo-', 'LineWidth', 2);
    title('各场景能耗对比');
    xlabel('场景');
    ylabel('能耗 (mW)');
    set(gca, 'XTick', 1:length(results));
    scenario_names = cell(length(scenarios), 1);
    for i = 1:length(scenarios)
        scenario_names{i} = scenarios{i}.name;
    end
    set(gca, 'XTickLabel', scenario_names);
    xtickangle(45);
    grid on;

    % PDR对比
    subplot(2, 2, 2);
    errorbar(1:length(results), mean_pdr, ci_pdr, 'ro-', 'LineWidth', 2);
    title('各场景PDR对比');
    xlabel('场景');
    ylabel('PDR');
    set(gca, 'XTick', 1:length(results));
    set(gca, 'XTickLabel', scenario_names);
    xtickangle(45);
    grid on;
    
    fprintf('对比图已生成\n');
end
