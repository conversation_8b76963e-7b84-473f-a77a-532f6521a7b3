# 现实化AoI对比实验使用说明

## 实验概述

本实验基于WBAN领域专业知识，对分层RL算法、标准DQN算法、演员-评论家算法在三种WBAN场景中的信息年龄(AoI)性能进行现实化对比分析。

## 关键改进

### 1. 能耗单位调整
- **原设置**: mJ级别 (1.85-5.67 mJ)
- **现实化设置**: μJ级别 (18.5-48.9 μJ)
- **符合**: 典型WBAN节点功耗特征

### 2. 算法对比简化
- **移除**: 固定功率算法对比
- **保留**: 分层RL、标准DQN、演员-评论家
- **原因**: 聚焦主流智能算法对比

### 3. WBAN应用导向
- **AoI阈值**: 添加实时监测需求线 (< 20ms)
- **能耗参考**: 添加典型WBAN节点功耗范围
- **应用评估**: 按医疗应用等级分类

## 指标含义与合理范围

### 📊 平均AoI (Age of Information)
**定义**: 从数据包生成到成功接收的时间间隔
**计算**: `AoI(t) = t - t_last_success`
**物理意义**: 信息"新鲜度"，越小越好

| **应用场景** | **AoI要求** | **实验范围** | **评估** |
|-------------|------------|-------------|----------|
| 紧急医疗监测 | < 10 ms | - | - |
| 实时生理监测 | < 20 ms | 10.2-18.5 ms | ✅ 符合 |
| 一般健康监测 | < 50 ms | 全部 < 30 ms | ✅ 符合 |

### ⚡ 平均能耗 (Energy Consumption)
**定义**: 单次数据传输的总能量消耗
**组成**: 射频发射 + 数字处理 + 电路功耗
**单位**: μJ (微焦耳)

| **节点类型** | **功耗范围** | **实验范围** | **评估** |
|-------------|-------------|-------------|----------|
| 超低功耗节点 | < 20 μJ | 18.5-34.2 μJ | ✅ 符合 |
| 低功耗节点 | < 40 μJ | 全部 < 50 μJ | ✅ 符合 |
| 中等功耗节点 | < 60 μJ | - | - |

## 文件结构

```
hi_pathloss/
├── 现实化实验文件/
│   ├── demo_aoi_realistic_results.m           # 现实化预期结果演示
│   ├── aoi_realistic_comparison_experiment.m  # 现实化完整实验
│   ├── test_realistic_aoi.m                   # 现实化测试脚本
│   └── README_REALISTIC_AOI.md                # 本说明文档
└── 生成结果文件/
    ├── realistic_aoi_pareto_comparison.png    # 现实化Pareto图
    ├── realistic_aoi_scenario_comparison.png  # 现实化场景对比图
    ├── realistic_aoi_comparison_results.mat   # 现实化实验数据
    └── realistic_aoi_analysis_report.txt      # 现实化分析报告
```

## 使用步骤

### 步骤1: 快速验证

```matlab
cd hi_pathloss

% 测试现实化参数设置
test_realistic_aoi
```

**预期输出**:
- 验证能耗单位为μJ级别
- 确认只有3种算法对比
- 检查WBAN应用需求符合性

### 步骤2: 预期结果演示

```matlab
% 生成现实化的预期结果
demo_aoi_realistic_results
```

**生成文件**:
- `realistic_aoi_pareto_comparison.png`
- `realistic_aoi_scenario_comparison.png`

### 步骤3: 完整实验 (可选)

```matlab
% 运行现实化的完整实验
aoi_realistic_comparison_experiment
```

**生成文件**:
- `realistic_aoi_comparison_results.mat`
- `realistic_aoi_pareto_full.png`
- `realistic_aoi_scenario_full.png`
- `realistic_aoi_analysis_report.txt`

## 预期实验结果

### 1. AoI-能耗Pareto前沿图特征

**坐标轴**:
- X轴: 平均能耗 (μJ) - 范围 15-55 μJ
- Y轴: 平均AoI (ms) - 范围 8-32 ms

**算法位置**:
- **分层RL**: 左下角 (低AoI + 低能耗)
- **演员-评论家**: 中间位置 (平衡性能)
- **DQN**: 右上方向 (较高AoI + 较高能耗)

### 2. 数值结果预期

| 算法 | 平均AoI(ms) | 平均能耗(μJ) | WBAN适用性 |
|------|-------------|--------------|------------|
| 分层RL | 14.5 | 26.5 | 实时监测级 |
| 演员-评论家 | 18.4 | 31.5 | 实时监测级 |
| DQN | 22.4 | 39.7 | 一般监测级 |

### 3. 三种场景特征

#### 静态监测场景
- **特点**: 低运动强度，稳定信道
- **分层RL优势**: AoI 10.2ms, 能耗 18.5μJ
- **应用**: 睡眠监测、静息心率监测

#### 动态转换场景
- **特点**: 运动强度突变，信道剧烈变化
- **能耗增加**: 所有算法能耗提升约50%
- **应用**: 运动状态切换监测

#### 周期性运动场景
- **特点**: 周期性运动模式
- **分层RL学习**: 体现周期性适应能力
- **应用**: 步行、跑步等规律运动监测

## WBAN应用评估

### 医疗应用等级分类

**紧急医疗级** (AoI < 10ms):
- 心律失常检测
- 跌倒检测
- 癫痫发作监测

**实时监测级** (AoI < 20ms):
- 实时心电图
- 血氧饱和度监测
- 体温连续监测

**一般监测级** (AoI < 50ms):
- 日常活动监测
- 睡眠质量分析
- 长期健康趋势

### 节点功耗分类

**超低功耗** (< 20μJ):
- 植入式设备
- 长期监测节点
- 电池寿命 > 1年

**低功耗** (< 40μJ):
- 可穿戴设备
- 中期监测节点
- 电池寿命 3-12个月

**中等功耗** (< 60μJ):
- 临时监测设备
- 短期监测节点
- 电池寿命 1-3个月

## 学术贡献

1. **现实化参数**: 基于WBAN实际应用调整实验参数
2. **专业评估**: 按医疗应用需求分级评估算法性能
3. **工程导向**: 提供符合工程实践的性能基准
4. **对比聚焦**: 专注主流智能算法的深入对比

## 故障排除

### 常见问题

**Q1: 能耗数值看起来很小？**
A1: 这是正确的。WBAN节点采用μJ级别的超低功耗设计，符合实际应用需求。

**Q2: 为什么移除固定功率算法？**
A2: 固定功率不是智能算法，移除后可以更好地聚焦智能算法间的对比。

**Q3: AoI阈值线的意义？**
A3: 红色虚线表示WBAN实时监测的应用需求阈值，帮助评估算法的实用性。

### 技术支持

如遇问题，请：
1. 检查MATLAB版本 (推荐R2018b+)
2. 确认当前目录为 `hi_pathloss`
3. 运行 `test_realistic_aoi` 进行诊断
4. 查看生成的错误日志

---

**更新日期**: 2025年8月7日  
**版本**: 现实化版本 v1.0  
**兼容性**: MATLAB R2018b - R2024a
