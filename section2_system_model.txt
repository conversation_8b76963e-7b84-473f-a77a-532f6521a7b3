2 系统模型与问题建模

无线体域网（WBAN）作为一种专门针对人体周围通信的短距离无线网络，其系统架构和通信特性与传统无线传感器网络存在显著差异。本节将详细阐述WBAN的网络拓扑结构、信道传播特性、节点能耗模型，并在此基础上形式化定义功率控制优化问题。

2.1 WBAN网络拓扑与通信流程

典型的WBAN系统采用星型拓扑结构，由一个中央协调器（Central Coordinator, CC）和多个分布式传感器节点组成。中央协调器通常部署在人体躯干位置，具备相对充足的计算和存储资源，负责网络管理、数据汇聚和对外通信功能。传感器节点则根据监测需求分布在人体的不同部位，如心电监测节点位于胸部、血氧监测节点位于手指、温度传感器位于腋下等。这些节点具有严格的尺寸和功耗限制，通常采用纽扣电池供电，电池容量在几十到几百毫安时之间。

在通信流程方面，WBAN采用基于时分多址（TDMA）的MAC协议来避免节点间的相互干扰。每个超帧周期被划分为若干时隙，传感器节点在分配的时隙内向中央协调器发送数据。为了进一步降低能耗，节点在非通信时隙进入睡眠状态。这种周期性的睡眠-唤醒机制虽然有效降低了平均功耗，但也对功率控制策略的实时性和适应性提出了挑战。

2.2 信道与传播模型

WBAN信道环境的复杂性主要源于人体组织的电磁特性和人体运动的动态性。人体组织具有高介电常数（εᵣ≈50-80）和导电性（σ≈0.5-2 S/m），对2.4GHz ISM频段的电磁波传播产生显著的吸收和散射效应。同时，人体的非均匀性导致不同身体部位之间的传播特性存在较大差异。例如，胸部到手腕的链路主要经历自由空间传播和人体表面的绕射，而胸部到腰部的链路则可能涉及人体组织的穿透传播。

为了准确建模这种复杂的传播环境，本研究采用基于实测数据的统计信道模型，该模型基于对20名不同体型受试者（BMI范围18.5-30.2）在多种日常活动场景下的信道测量数据。路径损耗可以表示为：

PL(d,f,θ) = PL₀(f) + 10n(f)log₁₀(d/d₀) + Xσ(θ) + ΔPL(θ)

其中，PL₀(f)为频率f下参考距离d₀=10cm处的路径损耗，n(f)为频率相关的路径损耗指数，Xσ(θ)为姿态相关的零均值高斯阴影衰落（标准差σ=3-8dB），ΔPL(θ)为与人体姿态θ相关的附加损耗。通过大量实测数据的统计分析，确定了2.4GHz频段下的模型参数：PL₀=40.2dB，n=3.38，这与IEEE 802.15.6标准的CM3信道模型具有良好的一致性。

考虑到人体运动的周期性和可预测性，本研究将人体姿态建模为K=8状态的有限马尔可夫链，包括站立、坐下、行走、跑步、弯腰、举手、侧卧和仰卧等典型姿态。状态转移概率矩阵P通过对100小时的人体活动数据统计获得，其中对角元素Pᵢᵢ>0.85反映了姿态的相对稳定性。每个姿态状态θₖ对应一组特定的路径损耗参数{PL₀ₖ, nₖ, σₖ}，这种建模方法既保持了信道模型的准确性（平均预测误差<3dB），又为分层强化学习算法的设计提供了理论基础。

为验证模型的有效性，本研究将所提模型与IEEE 802.15.6标准模型在相同测试场景下进行了对比，结果表明所提模型在动态场景下的预测精度提高了约15%，特别是在姿态转换期间的信道预测准确性显著改善。

2.3 传感器节点能耗模型

传感器节点的能耗主要包括四个部分：射频收发电路功耗、数字信号处理功耗、传感器采集功耗和微控制器运行功耗。基于对典型WBAN节点（如CC2420射频芯片和MSP430微控制器）的详细功耗测量，射频收发电路功耗占据主导地位，约占总功耗的60-80%。节点的功耗状态可以分为四种：睡眠状态（P_sleep=3μW）、监听状态（P_listen=62mW）、接收状态（P_rx=56mW）和发送状态（P_tx_total）。

在发送状态下，节点的总功耗可以表示为：

P_tx_total = P_tx + P_circuit + P_PA + P_DSP + P_sensor

其中，P_tx为射频输出功率（-25dBm到0dBm），P_circuit为电路固定功耗（17mW），P_PA为功率放大器功耗，P_DSP为数字信号处理功耗（包括调制、编码等，约5-12mW），P_sensor为传感器采集功耗（根据采样率fs变化，P_sensor=α·fs+β，其中α=0.1mW/Hz，β=2mW）。

功率放大器的效率呈非线性特性，基于实测数据建立的效率模型为：

η(P_tx) = η_max · (1 - exp(-γ·P_tx/P_sat))

P_PA = P_tx/η(P_tx) + P_bias

其中，η_max=35%为最大效率，γ=2.3为形状参数，P_sat=-10dBm为饱和功率，P_bias=8mW为偏置功耗。这种非线性关系表明，在低功率区域功率放大器效率较低，简单地降低发射功率并不总是能够实现最优的能效比。

考虑到锂电池的动态特性，节点的剩余能量估计采用改进的库仑计数法结合开路电压法：

E_i(t) = E_i^(0) · (1-α_aging·t/T_life) - ∫₀ᵗ P_i(τ)·k_temp(T(τ))dτ + ε_noise(t)

其中，E_i^(0)为节点i的初始电池容量，α_aging=0.02为年老化系数，T_life为电池设计寿命，k_temp(T)为温度修正系数，ε_noise(t)为测量噪声（标准差约为实际容量的2%）。当剩余能量降至阈值E_min=10%·E_i^(0)以下时，节点将无法正常工作，导致网络连通性的破坏。

2.4 问题描述：在保证链路可靠性的前提下最小化整体能耗

基于上述系统模型，WBAN功率控制问题可以形式化为一个多目标约束优化问题。设网络中有N个传感器节点（i∈{1,2,...,N}），时间被离散化为T个时隙（t∈{1,2,...,T}），每个节点可以选择的发射功率级别为离散集合P = {p₁, p₂, ..., p_M}，其中M=8，功率范围为{-25,-20,-15,-10,-5,0}dBm。在时隙t，节点i选择发射功率p_i(t) ∈ P，对应的接收信号强度为：

RSSI_i(t) = p_i(t) - PL_i(t,θ(t)) + G_tx + G_rx + ε_measure(t)

其中，PL_i(t,θ(t))为时隙t在姿态θ(t)下的路径损耗，G_tx=2dBi和G_rx=2dBi分别为发射和接收天线增益，ε_measure(t)为RSSI测量噪声（标准差σ_measure=2dB）。

链路可靠性约束要求接收信号强度不低于预设阈值：

RSSI_i(t) ≥ RSSI_min,i, ∀i∈{1,...,N}, ∀t∈{1,...,T}

其中，RSSI_min,i根据节点i的数据类型确定：心电节点为-85dBm，血氧节点为-80dBm，温度节点为-75dBm。

能量约束确保网络长期运行：

E_i(t) ≥ E_min,i, ∀i∈{1,...,N}, ∀t∈{1,...,T}

其中，E_min,i = 0.1·E_i^(0)为节点i正常工作所需的最小剩余能量。

QoS约束考虑不同数据类型的时延要求：

D_i(t) ≤ D_max,i, ∀i∈{1,...,N}, ∀t∈{1,...,T}

其中，D_i(t)为节点i在时隙t的数据传输延迟，D_max,i为最大可容忍延迟（心电：100ms，血氧：500ms，温度：5s）。

在满足上述约束的前提下，多目标优化问题可表述为：

minimize f₁ = ∑ᵢ₌₁ᴺ ∑ₜ₌₁ᵀ P_tx_total,i(t) + P_CC_rx(t)        (系统总能耗最小化)
maximize f₂ = ∑ᵢ₌₁ᴺ ∑ₜ₌₁ᵀ w_i·(RSSI_i(t) - RSSI_min,i)        (链路余量最大化)

subject to: RSSI_i(t) ≥ RSSI_min,i, ∀i∈{1,...,N}, ∀t∈{1,...,T}
           E_i(t) ≥ E_min,i, ∀i∈{1,...,N}, ∀t∈{1,...,T}
           D_i(t) ≤ D_max,i, ∀i∈{1,...,N}, ∀t∈{1,...,T}
           p_i(t) ∈ P, ∀i∈{1,...,N}, ∀t∈{1,...,T}

其中，w_i为节点i的重要性权重，(RSSI_i(t) - RSSI_min,i)表示链路质量余量。

**等价的单目标加权形式：**
minimize f = λ₁·∑ᵢ₌₁ᴺ ∑ₜ₌₁ᵀ P_tx_total,i(t) - λ₂·∑ᵢ₌₁ᴺ ∑ₜ₌₁ᵀ w_i·(RSSI_i(t) - RSSI_min,i)

其中，λ₁和λ₂为权重系数，用于平衡能耗与可靠性的重要性。

**中央协调器接收功耗模型：**
P_CC_rx(t) = P_CC_circuit + ∑ᵢ₌₁ᴺ [P_CC_rx_i(t)·δᵢ(t) + P_CC_process_i(t)]

其中：
- P_CC_circuit：协调器电路基础功耗（约80mW）
- P_CC_rx_i(t)：接收节点i数据的功耗（约56mW，当δᵢ(t)=1时激活）
- δᵢ(t)：节点i在时隙t的传输指示函数
- P_CC_process_i(t)：处理节点i数据的功耗（与数据量和处理复杂度相关，约5-15mW）

**设计说明：**
1. **传感器节点能耗**：∑P_tx_total,i(t)为可控的优化目标，直接受功率控制策略影响
2. **协调器接收能耗**：P_CC_rx(t)为系统运行的必要开销，虽不直接受功率控制影响，但影响系统总能耗评估
3. **耦合关系**：传感器节点发射功率影响协调器的接收信号质量，进而影响重传概率和处理复杂度

**复杂度分析：** 该问题属于NP-hard问题。证明可通过将其归约为多维背包问题：每个时隙的功率选择对应一个物品，功率级别对应物品重量，能耗对应物品价值，约束条件对应背包容量限制。状态空间复杂度为O(M^(N·T))，在实际WBAN场景下（N=8，T=1000，M=8）达到10^2700，传统动态规划方法无法求解。

**实现考虑：** 考虑到实际系统的限制，引入以下实现约束：
1) RSSI测量延迟：τ_measure = 5ms
2) 功率控制执行延迟：τ_control = 10ms
3) 状态信息上报开销：每次上报消耗额外0.5mJ能量
4) 中央协调器处理能力：最大支持100次/秒的功率调整

该问题的动态性、多约束性和大规模状态空间使得传统优化方法（如线性规划、贪心算法）难以获得满意的解决方案。分层强化学习方法通过将复杂问题分解为高层姿态适应和低层功率优化两个子问题，能够有效降低问题复杂度，同时保持解的质量，为WBAN功率控制提供了可行的解决方案。
