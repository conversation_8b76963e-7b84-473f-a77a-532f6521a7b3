2 系统模型与问题建模（修订版）

2.1 WBAN网络拓扑与通信流程

无线体域网(WBAN)是一种专门针对人体环境设计的短距离无线通信网络，通常由分布在人体表面或植入体内的多个传感器节点构成。本研究考虑的WBAN网络拓扑采用星型拓扑结构，其中包含一个中心协调器节点(通常位于腰部或胸部)和若干个传感器节点分布在人体的不同部位，如手腕、胸部、背部等关键监测位置。传感器节点负责采集生理信号数据，包括心电图(ECG)、肌电图(EMG)、加速度等生物医学参数，并通过无线链路将数据传输至协调器节点。协调器节点作为数据汇聚点，不仅接收来自各传感器节点的数据，还负责网络的整体协调和管理，包括信道接入控制、功率管理和数据转发等功能。

在通信流程方面，WBAN采用基于时分多址(TDMA)的MAC协议来避免节点间的相互干扰。每个传感器节点在分配的时隙内进行数据传输，传输过程遵循IEEE 802.15.6标准规范。考虑到人体运动的周期性特征，本研究将运动模式分为三类：静态监测(如睡眠、静坐)、周期性运动(如行走、跑步)和动态转换(如从坐姿转为站立)。不同运动模式下，信道相关时间分别约为100ms、50ms和20ms，对应的最大多普勒频移分别为2Hz、8Hz和15Hz。这些参数的量化为功率控制算法的自适应调整提供了时间尺度参考。

2.2 信道与传播模型

WBAN的信道环境具有高度的时变特性和复杂性，主要体现在路径损耗的非线性变化和多径衰落效应。本研究基于IEEE 802.15.6标准中的CM3(Body-to-Body)和CM4(Body Surface-to-Body Surface)信道模型，结合实测数据建立了针对人体信道的改进路径损耗模型。

对于体表通信链路，路径损耗可以表示为：
PL(d,θ) = PL₀ + 10n(θ) log₁₀(d/d₀) + Xσ + Gshadow(θ)

其中PL₀为参考距离d₀=10cm处的路径损耗(约为40dB)，n(θ)为，d为节点间距离，Xσ为零均值高斯阴影衰落项(标准差σ=3.5dB)，Gshadow(θ)为姿态相关的阴影损耗项。

根据文献[Smith et al., 2018]和本研究的实测数据分析，路径损耗指数n(θ)可以建模为：
n(θ) = n₀ + Δn·cos²(θ/2)

其中n₀=2.3为基础路径损耗指数，Δn=1.8为姿态调节参数，θ为身体弯曲角度。这样，当人体直立时(θ=0°)，n≈4.1；当人体弯腰时(θ=90°)，n≈2.3。该模型与文献中报告的n值范围(2.0-4.5)保持一致。

为了考虑节点间的同频干扰，接收端的信干噪比(SINR)可以表示为：
SINR_i = (P_tx,i · G_i) / (N₀ + Σⱼ≠ᵢ P_tx,j · G_j · I_ij)

其中P_tx,i为节点i的发射功率，G_i为对应的信道增益，N₀为热噪声功率，I_ij为节点j对节点i的干扰系数(取决于时间同步精度和频谱泄漏)。

2.3 传感器节点能耗模型

基于Texas Instruments CC2420射频芯片的实际参数，本研究建立了更精确的节点能耗模型。节点的总能耗包括射频收发能耗、数字基带处理能耗和电路静态功耗三部分。

发射端能耗模型：
E_tx = (P_PA + P_synth + P_DAC + P_filter) × T_tx

其中各项功耗为：
- 功率放大器功耗：P_PA = P_tx/η + P_bias，η为功放效率(0.18)，P_bias为偏置功耗(15mW)
- 频率合成器功耗：P_synth = 18mW
- 数模转换器功耗：P_DAC = 8.5mW  
- 滤波器功耗：P_filter = 2.5mW

接收端能耗模型：
E_rx = (P_LNA + P_mixer + P_ADC + P_synth) × T_rx = 56mW × T_rx

其中P_LNA为低噪声放大器功耗(20mW)，P_mixer为混频器功耗(16mW)，P_ADC为模数转换器功耗(2mW)。

考虑到BPSK调制和Manchester编码的处理开销，数字基带处理能耗为：
E_bb = P_MCU × (T_encode + T_decode) = 12mW × T_process

节点在睡眠状态下的功耗为P_sleep = 0.02mW，在监听状态下的功耗等同于接收功耗。

2.4 问题描述：在保证链路可靠性的前提下最小化整体能耗

本研究的核心问题可以表述为：在满足WBAN通信可靠性要求的前提下，通过优化各节点在时间窗口T内的发射功率序列，实现网络整体能耗的最小化。这是一个动态多目标约束优化问题，需要在能耗、可靠性和服务质量之间寻求最优平衡。

形式化地，该优化问题可以表述为：

minimize Σᵢ₌₁ᴺ Σₜ₌₁ᵀ [E_tx,i(P_i(t)) + E_rx,i + E_idle,i]

subject to:
- SINRᵢ(t) ≥ SINRᵢ,th, ∀i ∈ N, ∀t ∈ T (信干噪比约束)
- PRᵢ = Σₜ (SINRᵢ(t) ≥ SINRᵢ,th)/T ≥ PRᵢ,min, ∀i ∈ N (时间平均包成功率约束)
- Pᵢ,min ≤ Pᵢ(t) ≤ Pᵢ,max, ∀i ∈ N, ∀t ∈ T (功率范围约束)
- Σₜ₌₁ᵀ Eᵢ(t) ≤ Eᵢ,battery, ∀i ∈ N (电池容量约束)
- max{tᵢ,delay} ≤ Dᵢ,max, ∀i ∈ N (端到端时延约束)

其中，N为网络中传感器节点总数，T为优化时间窗口长度，Eᵢ(t)表示节点i在时刻t的瞬时总能耗，SINRᵢ,th为信干噪比门限(通常设为6dB以保证误码率低于10⁻³)，PRᵢ,min为最小包成功率要求(通常为95%)，Eᵢ,battery为节点i的剩余电池容量，tᵢ,delay为数据包的端到端传输时延。

该问题的主要挑战在于：首先，人体信道的快速时变特性(相关时间20-100ms)要求功率控制算法具备毫秒级的响应速度；其次，多节点间的同频干扰耦合使得问题的解空间呈现强非凸特性；最后，问题的状态空间维度随着节点数量N和时间步数T呈O(N×T×|P|)增长，其中|P|为离散功率级别数，传统的动态规划方法面临维度诅咒问题。

为了有效求解这一NP-hard的组合优化问题，本研究提出采用分层强化学习框架。高层策略负责检测人体运动状态变化并预测信道变化趋势，低层策略基于当前信道状态和高层指导进行具体的功率分配决策。这种分层架构能够有效降低问题的复杂度，同时保持对动态环境的快速适应能力。 