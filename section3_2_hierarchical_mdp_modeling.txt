3.2 分层MDP建模

分层强化学习的核心在于将复杂的决策问题分解为多个相互协调的子问题，每个子问题都可以建模为独立的马尔可夫决策过程。在WBAN功率控制场景中，本文采用双层MDP架构，将原始的高维复杂优化问题分解为高层的环境适应策略选择和低层的具体功率控制两个层次。这种分层建模不仅有效降低了单个MDP的状态空间维度，还充分利用了WBAN系统固有的时间尺度分离特性，实现了长期战略规划与短期实时控制的有机结合。

3.2.1 高层马尔可夫决策过程建模：环境适应策略
高层MDP负责环境变化的检测和适应策略的选择，决策周期为秒级。通过对真实WBAN数据的主成分分析，我们将高层状态空间设计为包含信道时间相关性系数、RSSI标准差和网络整体能耗水平的三维向量S_h = {R_corr(t), σ_RSSI(t), E_network(t)}。信道时间相关性系数量化了信道变化的平滑程度，RSSI标准差反映了信道波动的剧烈程度，而网络整体能耗水平则通过指数加权移动平均来计算。这三个维度能够解释环境变化的89.2%方差，确保了状态空间的完备性。

高层动作空间包含三种策略选择：保持当前低层策略以避免不必要的计算开销、在环境显著变化时重新训练低层网络以确保最优性，以及通过微调关键参数来平衡适应性和计算效率。奖励函数采用网络寿命延长率作为主要指标，即R_high = (T_current - T_baseline)/T_baseline，并加入切换成本惩罚项以避免频繁的策略调整。这种设计避免了难以量化的"适应性"概念，转而采用可直接测量的性能指标，提高了算法的实用性和可解释性。


3.2.2 低层马尔可夫决策过程建模：功率控制优化

低层MDP专注于毫秒级的实时功率控制决策，其核心任务是在满足通信可靠性约束的前提下最小化节点能耗。低层状态空间设计为S_l = {RSSI_i(t), E_i(t), Q_i(t)}的3N维向量，其中N为网络节点数量。RSSI_i(t)表示节点i在时刻t的接收信号强度指示，是信道质量的直接反映，决定了维持可靠通信所需的最低发射功率；E_i(t)代表节点i的剩余电量百分比，是能耗约束的关键参数，当剩余电量过低时算法需要采取更加激进的节能策略；Q_i(t)表示节点i的数据队列长度，反映了数据传输的紧迫性，队列越长说明需要更高的传输可靠性。

低层动作空间定义为离散的发射功率级别A_l = {-25, -20, -15, -10, -5, 0} dBm，共六个选项。这种离散化设计既简化了动作选择的复杂度，又与实际硬件的功率调节能力相匹配。在单次通信时隙内（5ms），发射功率保持恒定，因为发射功率与数据量之间存在非线性关系，保持恒定可以最大化能量利用效率。当需要超出限制的发射功率时，系统会进行剪裁处理并给予相应的惩罚，确保硬件安全和策略的可行性。

低层状态转移概率隐含地反映了WBAN信道的时变特性和节点能耗的动态变化。由于本研究采用无模型强化学习方法，算法通过与环境的直接交互学习状态转移规律，无需事先建立精确的状态转移概率模型。这种设计选择不仅避免了复杂信道建模的困难，还能够自适应地处理未建模的环境动态。

低层奖励函数设计基于拉格朗日对偶理论，将原始约束优化问题转化为无约束问题。具体而言，原问题为：min P_total s.t. RSSI ≥ RSSI_min，其拉格朗日函数为L = P_total + μ·max(0, RSSI_min - RSSI)。通过指示函数近似I(RSSI ≥ RSSI_min) ≈ -μ·max(0, RSSI_min - RSSI)，可得到等价的奖励函数形式R_low = -λ·P_total + (1-λ)·I(RSSI ≥ RSSI_min)，其中λ = 1/(1+μ)为归一化的拉格朗日乘子。理论分析表明，当λ通过网格搜索优化确定时，该奖励函数能够收敛到原问题的帕累托最优解。

3.2.3 层次间接口设计

分层MDP的有效运行依赖于高层和低层之间清晰的接口机制。接口函数将高层动作和低层状态映射为低层网络参数的调整策略，包括网络权重、学习率和探索率三个关键要素。在保持模式下，所有低层参数保持不变以维持系统稳定性；在调参模式下，保持网络权重不变但根据环境稳定性自适应调整学习率，同时基于Q值方差动态设定探索率；在重训练模式下，网络权重重新随机初始化并恢复默认参数设置。

系统采用定期反馈机制，低层每10秒向高层报告关键性能指标，包括平均能耗、包成功率和网络剩余寿命等，这些指标用于计算高层奖励函数并指导后续决策。时间同步设计充分利用了WBAN环境的时间尺度分离特性，考虑到信道相关时间为20-100毫秒而人体姿态变化时间为5-30秒，我们将高层决策周期设置为1秒，低层决策周期设置为5毫秒，形成200:1的时间尺度比。

基于Options理论框架，系统收敛性通过三个条件得到保证：每个低层策略在固定option下的收敛性、高层策略满足ε-greedy探索条件，以及层间切换频率的有界性（最多每秒一次）。实验验证表明，当时间尺度比在100:1到500:1范围内时，系统性能保持稳定，证实了接口设计的合理性和有效性。