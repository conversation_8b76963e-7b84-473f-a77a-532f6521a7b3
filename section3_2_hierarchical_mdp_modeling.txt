在强化学习中，MDP通常用于描述环境信息，智能体通过与环境持续交互实现自我学习及决策优化。在本文研究场景中，中央协调器被视为智能体。
a)高层MDP建模：环境适应策略。高层MDP专注于环境变化的检测和适应策略的选择，其决策周期设置为秒级，主要负责监测信道统计特性的变化并相应调整低层控制策略。高层状态空间设计为三维向量S_h = {R_corr(t), σ_RSSI(t), E_network(t)}，该设计基于WBAN环境特征的主成分分析结果。其中R_corr(t)表示当前时刻信道的时间相关性系数，通过对连续RSSI测量值进行滞后τ=50ms的自相关分析获得：R_corr(t) = E[RSSI(t)·RSSI(t-τ)]/σ²_RSSI，用于量化信道变化的平滑程度；σ_RSSI(t)代表RSSI测量值在时间窗口T_w=10s内的标准差，反映信道波动的剧烈程度，是环境动态性的关键指标；E_network(t)表示网络整体能耗水平，通过指数加权移动平均E_network(t) = αE_current + (1-α)E_network(t-1)计算，其中α=0.1为平滑因子，用于评估当前策略的长期能效表现。
高层动作空间被设计为三个离散选项A_h = {保持, 重训练, 调参}，对应不同程度的策略调整。"保持"动作表示当前环境稳定，低层策略无需改变，这是最常见的选择，能够避免不必要的计算开销；"重训练"动作在检测到显著环境变化时触发，要求低层DQN网络重新学习以适应新环境，虽然计算成本较高但能确保策略的最优性；"调参"动作介于两者之间，仅对低层网络的关键参数（如学习率、探索率）进行微调，在适应性和计算效率之间取得平衡。高层状态转移反映了WBAN环境的渐变特性，即环境参数通常不会发生突变，而是呈现缓慢变化的趋势，这为高层策略的稳定性提供了理论保证。
高层奖励函数设计重点关注长期性能指标，主要组成部分为网络寿命延长率R_high = (T_current - T_baseline)/T_baseline，其中T_current表示采用当前策略后的网络预期寿命，T_baseline为基准策略（如固定功率策略）的网络寿命。这种设计避免了传统研究中"适应性"等难以量化概念的定义困难，转而采用可直接测量和比较的性能指标。同时，为了避免频繁的策略切换带来的不稳定性，奖励函数中还包含了切换成本项，对重训练和调参动作施加适度的惩罚，鼓励算法在确实必要时才进行策略调整。
b)低层MDP建模：功率控制优化。低层MDP由五元组（S, A, P, r, γ）组成，其中：
状态(S)描述当前系统的状态，包含20维特征向量，具体包括：生物信号特征(6维)、信道状态特征(4维)、网络性能特征(4维)、环境上下文特征(4维)和历史性能特征(2维)。这种多模态状态表示能够全面刻画WBAN系统的动态特性。
动作（A）表示在当前状态下智能体可以采取的动作。在本问题中，动作定义为每个时隙的发射功率级别选择，具体动作空间为：A = {-25, -20, -15, -10, -5, 0} dBm，共6个离散功率级别。在单次通信时隙内（5ms），发送功率保持恒定，因为发送功率与数据量之间存在非线性关系，保持恒定可以最大化能量利用效率。发送功率的大小受缓存剩余数据量和电池剩余能量的限制，超出限制时需要进行剪裁处理。
状态转移概率（P）描述在某一状态下执行某个动作后，系统转移到下一个状态的概率分布。由于本研究采用无模型强化学习方法，算法通过与环境的直接交互学习最优策略，无需事先建立精确的状态转移概率模型。
奖励(r)反映智能体在某个状态下执行某一动作后获得的即时回报，综合考虑能耗效率、通信可靠性和服务质量要求。奖励函数采用动态权重机制，根据当前网络状态自适应调整能耗与可靠性的权衡，并基于缓存数据水平对能量溢出情况进行惩罚，促进智能体快速学习并优化决策策略。
折扣因子（γ）用于调整智能体在决策过程中对长期回报的重视程度，本研究设置γ=0.9，在即时奖励和长期规划之间取得平衡。
c)层次间接口设计。分层MDP的有效运行依赖于高层和低层之间清晰的接口机制。接口函数将高层动作和低层状态映射为低层网络参数的调整策略，包括网络权重、学习率和探索率三个关键要素。在保持模式下，所有低层参数保持不变以维持系统稳定性；在调参模式下，保持网络权重不变但根据环境稳定性自适应调整学习率，同时基于Q值方差动态设定探索率；在重训练模式下，网络权重重新随机初始化并恢复默认参数设置。
系统采用定期反馈机制，低层每10秒向高层报告关键性能指标，包括平均能耗、包成功率和网络剩余寿命等，这些指标用于计算高层奖励函数并指导后续决策。时间同步设计充分利用了WBAN环境的时间尺度分离特性，考虑到信道相关时间为20-100毫秒而人体姿态变化时间为5-30秒，我们将高层决策周期设置为1秒，低层决策周期设置为5毫秒，形成200:1的时间尺度比。
基于Options理论框架，系统收敛性通过三个条件得到保证：每个低层策略在固定option下的收敛性、高层策略满足ε-greedy探索条件，以及层间切换频率的有界性（最多每秒一次）。实验验证表明，当时间尺度比在100:1到500:1范围内时，系统性能保持稳定，证实了接口设计的合理性和有效性。

