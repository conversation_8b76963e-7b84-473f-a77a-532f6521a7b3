% 基本AoI功能测试脚本
% 验证修复后的代码是否正常工作

close all;
clear;
clc;

fprintf('=== 基本AoI功能测试 ===\n');

try
    % 测试1: 基本数据生成
    fprintf('测试1: 基本数据生成...');
    
    % 生成简单的测试数据
    algorithms = {'分层RL', 'DQN', '演员-评论家', '固定功率'};
    scenarios = {'静态监测', '动态转换', '周期性运动'};
    
    aoi_data = [
        [10.2, 18.5, 14.8];
        [16.8, 28.3, 22.1];
        [13.5, 23.2, 18.6];
        [22.4, 35.7, 28.9];
    ];
    
    energy_data = [
        [1.85, 3.42, 2.68];
        [3.15, 4.89, 3.87];
        [2.34, 3.98, 3.12];
        [4.25, 5.67, 4.98];
    ];
    
    fprintf(' ✓ 通过\n');
    
    % 测试2: 基本图表生成
    fprintf('测试2: 基本图表生成...');
    
    figure('Position', [100, 100, 800, 600]);
    
    % 创建简单的对比图
    subplot(2, 1, 1);
    bar(aoi_data');
    xlabel('场景');
    ylabel('平均AoI (ms)');
    title('AoI对比测试');
    legend(algorithms, 'Location', 'best');
    set(gca, 'XTickLabel', scenarios);
    
    subplot(2, 1, 2);
    bar(energy_data');
    xlabel('场景');
    ylabel('平均能耗 (mJ)');
    title('能耗对比测试');
    legend(algorithms, 'Location', 'best');
    set(gca, 'XTickLabel', scenarios);
    
    % 保存图片
    saveas(gcf, 'basic_aoi_test_result.png');
    saveas(gcf, 'basic_aoi_test_result.fig');
    
    fprintf(' ✓ 通过\n');
    
    % 测试3: Pareto图生成
    fprintf('测试3: Pareto图生成...');
    
    figure('Position', [200, 200, 1200, 400]);
    
    colors = [0.2, 0.6, 0.8; 0.8, 0.4, 0.2; 0.4, 0.8, 0.4; 0.6, 0.6, 0.6];
    markers = {'o', 's', '^', 'x'};
    
    for s = 1:length(scenarios)
        subplot(1, 3, s);
        hold on;
        
        for a = 1:length(algorithms)
            scatter(energy_data(a, s), aoi_data(a, s), 100, colors(a, :), ...
                   'Marker', markers{a}, 'LineWidth', 2, 'DisplayName', algorithms{a});
        end
        
        xlabel('平均能耗 (mJ)');
        ylabel('平均AoI (ms)');
        title(sprintf('%s场景', scenarios{s}));
        legend('Location', 'best');
        grid on;
    end
    
    sgtitle('AoI-能耗 Pareto前沿图测试');
    
    % 保存图片
    saveas(gcf, 'basic_pareto_test.png');
    saveas(gcf, 'basic_pareto_test.fig');
    
    fprintf(' ✓ 通过\n');
    
    % 测试4: 数值分析
    fprintf('测试4: 数值分析...');
    
    % 计算总体性能
    overall_aoi = mean(aoi_data, 2);
    overall_energy = mean(energy_data, 2);
    aoi_energy_product = overall_aoi .* overall_energy;
    
    fprintf('\n--- 性能排名 ---\n');
    fprintf('%-12s %-12s %-12s %-12s\n', '算法', '平均AoI(ms)', '平均能耗(mJ)', 'AoI×能耗');
    fprintf('%-12s %-12s %-12s %-12s\n', '----', '----------', '----------', '--------');
    
    [~, rank_idx] = sort(aoi_energy_product);
    
    for i = 1:length(rank_idx)
        idx = rank_idx(i);
        fprintf('%-12s %-12.1f %-12.2f %-12.1f\n', ...
               algorithms{idx}, overall_aoi(idx), overall_energy(idx), aoi_energy_product(idx));
    end
    
    fprintf('\n测试4 ✓ 通过\n');
    
    % 测试5: 文件保存
    fprintf('测试5: 文件保存...');
    
    test_results = struct();
    test_results.algorithms = algorithms;
    test_results.scenarios = scenarios;
    test_results.aoi_data = aoi_data;
    test_results.energy_data = energy_data;
    
    save('basic_aoi_test_data.mat', 'test_results');
    
    fprintf(' ✓ 通过\n');
    
    fprintf('\n=== 所有测试通过！ ===\n');
    fprintf('生成的文件:\n');
    fprintf('✓ basic_aoi_test_result.png - 基本对比图\n');
    fprintf('✓ basic_pareto_test.png - Pareto前沿图\n');
    fprintf('✓ basic_aoi_test_data.mat - 测试数据\n');
    
    fprintf('\n现在可以运行完整的实验:\n');
    fprintf('1. demo_aoi_expected_results  %% 预期结果演示\n');
    fprintf('2. start_aoi_experiments      %% 完整实验流程\n');
    
catch ME
    fprintf(' ✗ 失败\n');
    fprintf('错误信息: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end
