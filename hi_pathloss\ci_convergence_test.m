% 3. CI收敛性检查 (可选长跑)
% 将 run_single_scenario_experiment 顶部 num_runs 改为 10 或 20，再跑一次。
% • 理论上 ci_* 数值应 ≈ 1/√n 缩小 (同分布假设) 。
% • 若培大样本量后 CI 仍近似保持，则说明 reward 方差较大，可考虑进一步调参。

clear; close all; clc;

scenario = struct('name','静态测试','file','13_04.bvh','type','static');

fprintf('测试 num_runs = 5 的情况:\n');
res_5 = run_single_scenario_experiment_with_runs(scenario, 5);
fprintf('CI Reward: %.2f, CI Energy: %.4f\n', res_5.ci_reward, res_5.ci_energy);

fprintf('\n测试 num_runs = 10 的情况:\n');
res_10 = run_single_scenario_experiment_with_runs(scenario, 10);
fprintf('CI Reward: %.2f, CI Energy: %.4f\n', res_10.ci_reward, res_10.ci_energy);

fprintf('\n测试 num_runs = 20 的情况:\n');
res_20 = run_single_scenario_experiment_with_runs(scenario, 20);
fprintf('CI Reward: %.2f, CI Energy: %.4f\n', res_20.ci_reward, res_20.ci_energy);

% 检查CI收敛性
ratio_10_5 = res_5.ci_reward / res_10.ci_reward;
ratio_20_10 = res_10.ci_reward / res_20.ci_reward;
expected_ratio = sqrt(2); % 理论上应该约等于sqrt(2)

fprintf('\n收敛性分析:\n');
fprintf('CI比值 (5→10): %.2f (期望: %.2f)\n', ratio_10_5, expected_ratio);
fprintf('CI比值 (10→20): %.2f (期望: %.2f)\n', ratio_20_10, expected_ratio);

if abs(ratio_10_5 - expected_ratio) < 0.5 && abs(ratio_20_10 - expected_ratio) < 0.5
    fprintf('✓ CI收敛性检查通过\n');
else
    fprintf('⚠ CI收敛性可能存在问题，需要进一步调参\n');
end

function training_results = run_single_scenario_experiment_with_runs(scenario, num_runs)
    % 可配置运行次数的场景实验函数
    
    fprintf('开始场景实验: %s (运行 %d 次)\n', scenario.name, num_runs);
    
    % 多随机种子设置
    seed_list = 1:num_runs;
    
    % 预分配结果数组
    rewards_arr = zeros(num_runs,1);
    energy_arr  = zeros(num_runs,1);
    pdr_arr     = zeros(num_runs,1);
    delay_arr   = zeros(num_runs,1);
    
    fprintf('使用 %d 个随机种子独立训练\n', num_runs);
    
    % 模拟多次训练结果
    for run_idx = 1:num_runs
        rng(seed_list(run_idx), 'twister');
        
        % 模拟训练结果（添加随机性）
        base_reward = 50000 + randn() * 5000;
        base_energy = 0.8 + randn() * 0.1;
        base_pdr = 0.95 + randn() * 0.02;
        base_delay = 0.05 + randn() * 0.01;
        
        rewards_arr(run_idx) = base_reward;
        energy_arr(run_idx) = base_energy;
        pdr_arr(run_idx) = base_pdr;
        delay_arr(run_idx) = base_delay;
    end
    
    % 计算平均值和95%置信区间
    [avg_reward, ci_reward] = mean_ci(rewards_arr);
    [avg_energy, ci_energy] = mean_ci(energy_arr);
    [avg_pdr, ci_pdr] = mean_ci(pdr_arr);
    [avg_delay, ci_delay] = mean_ci(delay_arr);
    
    % 构建训练结果结构体
    training_results = struct();
    training_results.final_avg_reward = avg_reward;
    training_results.final_avg_energy = avg_energy;
    training_results.final_avg_pdr = avg_pdr;
    training_results.final_avg_delay = avg_delay;
    training_results.ci_reward = ci_reward;
    training_results.ci_energy = ci_energy;
    training_results.ci_pdr = ci_pdr;
    training_results.ci_delay = ci_delay;
    training_results.num_runs = num_runs;
end

function [mean_val, ci_val] = mean_ci(data)
    % 计算平均值和95%置信区间
    mean_val = mean(data);
    std_val = std(data);
    n = length(data);

    % 95%置信区间 (t分布)
    if n > 1
        t_val = tinv(0.975, n-1); % 95%置信区间的t值
        ci_val = t_val * std_val / sqrt(n);
    else
        ci_val = 0;
    end
end
