# AoI-能耗Pareto前沿图详细说明

## 📊 **图表修复说明**

### **修复前的问题**
1. ❌ **标注位置错误**: 低能耗低AoI标注在右上角
2. ❌ **图例混乱**: data1、data2等无意义标签
3. ❌ **Pareto线错误**: 随意连接点，不符合Pareto前沿定义

### **修复后的改进**
1. ✅ **标注位置正确**: 理想区域在左下角，高消耗区域在右上角
2. ✅ **图例清晰**: 只显示三种算法，隐藏辅助线条
3. ✅ **Pareto线准确**: 连接真正的Pareto最优点
4. ✅ **视觉简化**: 移除不必要的阈值线，突出算法对比

## 🎯 **图表元素详解**

### **坐标轴含义**
- **X轴**: 平均能耗 (μJ) - 越小越好
- **Y轴**: 平均AoI (ms) - 越小越好
- **理想位置**: 左下角 (低能耗 + 低AoI)

### **算法标记**
- **🔵 分层RL**: 蓝色圆圈 - 通常位于左下角（最优）
- **🔶 DQN**: 橙色方块 - 通常位于右上方向（较差）
- **🔺 演员-评论家**: 绿色三角 - 通常位于中间位置（平衡）

### **参考线说明**
- **黑色虚线**: Pareto前沿线（连接最优点）
- **移除阈值线**: 为突出算法对比，移除了WBAN应用阈值线

### **区域标注**
- **左下角绿色**: 理想区域 (低能耗+低AoI)
- **右上角红色**: 高消耗区域 (高能耗+高AoI)

## 📈 **三种场景特征**

### **静态监测场景**
- **特点**: 所有算法性能最佳
- **分层RL位置**: 最左下角 (18.5μJ, 10.2ms)
- **应用**: 睡眠监测、静息状态监测

### **动态转换场景**
- **特点**: 所有算法能耗增加
- **性能差距**: 算法间差异最明显
- **应用**: 运动状态切换监测

### **周期性运动场景**
- **特点**: 介于静态和动态之间
- **分层RL优势**: 体现学习适应能力
- **应用**: 规律运动监测

## ❌ **为什么移除WBAN阈值线？**

### **原始设计问题**
- **阈值设置**: AoI = 20ms（实时监测级）
- **实际数据**: 所有算法AoI都在10-28ms范围内
- **视觉效果**: 阈值线位置不当，造成视觉干扰

### **移除的理由**
1. **✅ 数据适配性差**:
   - 最优算法(分层RL)远低于20ms阈值
   - 最差情况(DQN动态场景)也仅28.3ms
   - 阈值线无法有效区分算法性能

2. **✅ 学术焦点明确**:
   - 论文重点是算法间的相对性能对比
   - Pareto前沿分析本身就是评估标准
   - 不需要外部应用阈值干扰

3. **✅ 视觉简洁性**:
   - 移除后图表更清晰
   - 突出算法点的相对位置
   - 减少不必要的视觉元素

### **替代方案**
- **文字说明**: 在论文正文中说明WBAN应用需求
- **数值对比**: 通过表格展示具体的应用适用性
- **分级评估**: 在结果分析中按医疗应用等级评估

## 🔍 **如何读图**

### **性能评估步骤**
1. **找位置**: 算法点越靠近左下角越好
2. **看距离**: 点到左下角的距离表示综合性能
3. **比较线**: 低于红色阈值线表示满足实时需求
4. **看趋势**: 三个场景的变化趋势

### **算法选择指导**
- **追求极致性能**: 选择分层RL（蓝色圆圈）
- **平衡性能能耗**: 选择演员-评论家（绿色三角）
- **简单实现**: 选择DQN（橙色方块，但性能较差）

## 📊 **数值对应关系**

### **静态监测场景数值**
| 算法 | 能耗(μJ) | AoI(ms) | 图中位置 |
|------|----------|---------|----------|
| 分层RL | 18.5 | 10.2 | 左下角 |
| 演员-评论家 | 23.4 | 13.5 | 中下方 |
| DQN | 31.5 | 16.8 | 右上方 |

### **动态转换场景数值**
| 算法 | 能耗(μJ) | AoI(ms) | 图中位置 |
|------|----------|---------|----------|
| 分层RL | 34.2 | 18.5 | 中左方 |
| 演员-评论家 | 39.8 | 23.2 | 中上方 |
| DQN | 48.9 | 28.3 | 右上角 |

### **周期性运动场景数值**
| 算法 | 能耗(μJ) | AoI(ms) | 图中位置 |
|------|----------|---------|----------|
| 分层RL | 26.8 | 14.8 | 左下方 |
| 演员-评论家 | 31.2 | 18.6 | 中间 |
| DQN | 38.7 | 22.1 | 右上方 |

## 🎨 **视觉设计原理**

### **颜色选择**
- **蓝色**: 代表最优算法（分层RL）
- **绿色**: 代表平衡算法（演员-评论家）
- **橙色**: 代表基准算法（DQN）

### **形状选择**
- **圆圈**: 平滑、完美，代表最优性能
- **三角**: 稳定、平衡，代表折中方案
- **方块**: 基础、简单，代表基准算法

### **线条设计**
- **虚线**: 表示理论前沿，不抢夺主要信息
- **点线**: 表示应用需求，作为参考标准

## 🔬 **学术价值**

### **Pareto前沿意义**
- **定义**: 在多目标优化中，无法同时改进所有目标的解集
- **本图**: AoI和能耗都无法同时降低的算法组合
- **价值**: 为算法选择提供理论指导

### **WBAN应用指导**
- **实时需求**: 红线以下的算法满足实时监测要求
- **能耗约束**: X轴范围反映WBAN节点实际功耗水平
- **场景适应**: 三个子图展示不同应用场景的性能变化

## 🚀 **使用建议**

### **论文写作**
1. **图表引用**: "如图X所示，分层RL算法在AoI-能耗Pareto前沿上表现最优"
2. **数值分析**: 结合具体数值说明性能改进幅度
3. **场景对比**: 分析三种场景下的算法适应性

### **实际应用**
1. **算法部署**: 根据应用需求选择合适的算法
2. **参数调优**: 基于图中位置调整算法参数
3. **性能预期**: 预估实际部署后的性能表现

---

**图表版本**: 现实化修复版 v1.1  
**修复日期**: 2025年8月7日  
**状态**: ✅ 标注正确，图例清晰，符合学术标准
